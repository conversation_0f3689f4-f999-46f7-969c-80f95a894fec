[2025-06-20 08:00:50] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:00:50.686563Z"} 
[2025-06-20 08:00:50] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":422,"duration_ms":115.62,"response_size":186,"timestamp":"2025-06-20T08:00:50.771916Z"} 
[2025-06-20 08:01:00] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5a-f566-7328-966a-8fd504516d64","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:01:00.843344Z"} 
[2025-06-20 08:01:00] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5a-f566-7328-966a-8fd504516d64","status_code":410,"duration_ms":59.78,"response_size":147,"timestamp":"2025-06-20T08:01:00.880747Z"} 
[2025-06-20 08:01:13] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5b-2503-70f8-87d1-d114f4f433db","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:01:13.039659Z"} 
[2025-06-20 08:01:13] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5b-2503-70f8-87d1-d114f4f433db","status_code":410,"duration_ms":74.4,"response_size":171,"timestamp":"2025-06-20T08:01:13.085750Z"} 
[2025-06-20 08:01:24] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T08:01:24.336665Z"} 
[2025-06-20 08:01:24] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":94.52,"response_size":185,"timestamp":"2025-06-20T08:01:24.430019Z"} 
[2025-06-20 08:02:01] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T08:02:01.947363Z"} 
[2025-06-20 08:02:02] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":200,"duration_ms":112.25,"response_size":197,"timestamp":"2025-06-20T08:02:02.036419Z"} 
[2025-06-20 08:02:29] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5c-4f05-7388-870a-f7d2efd73ffd/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:02:29.316186Z"} 
[2025-06-20 08:02:29] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5c-4f05-7388-870a-f7d2efd73ffd/accept","status_code":422,"duration_ms":88.98,"response_size":223,"timestamp":"2025-06-20T08:02:29.384102Z"} 
[2025-06-20 08:03:00] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5c-ca26-7110-a431-856383b9c181/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:00.845921Z"} 
[2025-06-20 08:03:00] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5c-ca26-7110-a431-856383b9c181/accept","status_code":422,"duration_ms":95.78,"response_size":223,"timestamp":"2025-06-20T08:03:00.916974Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:21.059663Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations","status_code":200,"duration_ms":111.91,"response_size":1738,"timestamp":"2025-06-20T08:03:21.147805Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations?status=active","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"active"},"timestamp":"2025-06-20T08:03:21.229071Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations?status=active","status_code":200,"duration_ms":5.93,"response_size":1185,"timestamp":"2025-06-20T08:03:21.234903Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["106"],"content-type":["application/json"]},"body":{"name":"Test Organisation","code":"TEST001","details":{"industry":"Technology"},"remarks":"Test remarks"},"timestamp":"2025-06-20T08:03:21.282613Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":201,"duration_ms":48.13,"response_size":390,"timestamp":"2025-06-20T08:03:21.330647Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:21.381376Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":422,"duration_ms":20.89,"response_size":252,"timestamp":"2025-06-20T08:03:21.402135Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["47"],"content-type":["application/json"]},"body":{"name":"Test Organisation","code":"DUPLICATE"},"timestamp":"2025-06-20T08:03:21.456170Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":422,"duration_ms":8.12,"response_size":186,"timestamp":"2025-06-20T08:03:21.464194Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:21.513170Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":5.29,"response_size":492,"timestamp":"2025-06-20T08:03:21.518344Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:21.563962Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/999","status_code":404,"duration_ms":13.98,"response_size":135,"timestamp":"2025-06-20T08:03:21.577864Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["63"],"content-type":["application/json"]},"body":{"name":"New Name","code":"NEW001","remarks":"Updated remarks"},"timestamp":"2025-06-20T08:03:21.623738Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":16.44,"response_size":414,"timestamp":"2025-06-20T08:03:21.640049Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["19"],"content-type":["application/json"]},"body":{"name":"New Name"},"timestamp":"2025-06-20T08:03:21.685328Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/999","status_code":404,"duration_ms":4.38,"response_size":135,"timestamp":"2025-06-20T08:03:21.689620Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:21.736974Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":200,"duration_ms":5.64,"response_size":440,"timestamp":"2025-06-20T08:03:21.742509Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:21.789633Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":422,"duration_ms":6.88,"response_size":186,"timestamp":"2025-06-20T08:03:21.796426Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:21.849432Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations","status_code":401,"duration_ms":7.17,"response_size":145,"timestamp":"2025-06-20T08:03:21.856504Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:21.858464Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":401,"duration_ms":1.31,"response_size":145,"timestamp":"2025-06-20T08:03:21.859879Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:21.862301Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":401,"duration_ms":1.0,"response_size":145,"timestamp":"2025-06-20T08:03:21.863287Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:21.865120Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/1","status_code":401,"duration_ms":0.92,"response_size":145,"timestamp":"2025-06-20T08:03:21.866019Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:21.867962Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":401,"duration_ms":1.15,"response_size":145,"timestamp":"2025-06-20T08:03:21.869096Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:21.926497Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":6.42,"response_size":452,"timestamp":"2025-06-20T08:03:21.932775Z"} 
[2025-06-20 08:03:21] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/2","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:21.983758Z"} 
[2025-06-20 08:03:21] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/2","status_code":403,"duration_ms":9.86,"response_size":134,"timestamp":"2025-06-20T08:03:21.993508Z"} 
[2025-06-20 08:03:22] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:22.046440Z"} 
[2025-06-20 08:03:22] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":403,"duration_ms":5.36,"response_size":134,"timestamp":"2025-06-20T08:03:22.051640Z"} 
[2025-06-20 08:03:31] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:31.516553Z"} 
[2025-06-20 08:03:31] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":401,"duration_ms":68.99,"response_size":145,"timestamp":"2025-06-20T08:03:31.563116Z"} 
[2025-06-20 08:03:31] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:31.712909Z"} 
[2025-06-20 08:03:31] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":200,"duration_ms":33.3,"response_size":1619,"timestamp":"2025-06-20T08:03:31.746084Z"} 
[2025-06-20 08:03:31] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:31.811162Z"} 
[2025-06-20 08:03:31] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":200,"duration_ms":6.06,"response_size":667,"timestamp":"2025-06-20T08:03:31.817083Z"} 
[2025-06-20 08:03:31] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:31.866500Z"} 
[2025-06-20 08:03:31] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":403,"duration_ms":8.47,"response_size":134,"timestamp":"2025-06-20T08:03:31.874879Z"} 
[2025-06-20 08:03:31] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?role=member","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"role":"member"},"timestamp":"2025-06-20T08:03:31.969864Z"} 
[2025-06-20 08:03:31] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?role=member","status_code":200,"duration_ms":5.63,"response_size":1134,"timestamp":"2025-06-20T08:03:31.975354Z"} 
[2025-06-20 08:03:31] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?model_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"model_id":"1"},"timestamp":"2025-06-20T08:03:31.977731Z"} 
[2025-06-20 08:03:31] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?model_id=1","status_code":200,"duration_ms":4.29,"response_size":1132,"timestamp":"2025-06-20T08:03:31.981950Z"} 
[2025-06-20 08:03:31] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?model_type=App%5CModels%5COrganisation","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"model_type":"App\\Models\\Organisation"},"timestamp":"2025-06-20T08:03:31.983743Z"} 
[2025-06-20 08:03:31] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?model_type=App%5CModels%5COrganisation","status_code":200,"duration_ms":4.28,"response_size":1598,"timestamp":"2025-06-20T08:03:31.988020Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=valid","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"valid"},"timestamp":"2025-06-20T08:03:32.105567Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=valid","status_code":200,"duration_ms":7.07,"response_size":677,"timestamp":"2025-06-20T08:03:32.112540Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=expired","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"expired"},"timestamp":"2025-06-20T08:03:32.114958Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=expired","status_code":200,"duration_ms":4.92,"response_size":677,"timestamp":"2025-06-20T08:03:32.119870Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=used_up","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"used_up"},"timestamp":"2025-06-20T08:03:32.122344Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=used_up","status_code":200,"duration_ms":7.28,"response_size":677,"timestamp":"2025-06-20T08:03:32.129607Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?per_page=10","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"per_page":"10"},"timestamp":"2025-06-20T08:03:32.188072Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?per_page=10","status_code":200,"duration_ms":8.52,"response_size":4915,"timestamp":"2025-06-20T08:03:32.196479Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?page=2&per_page=10","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"per_page":"10","page":"2"},"timestamp":"2025-06-20T08:03:32.198354Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?page=2&per_page=10","status_code":200,"duration_ms":6.64,"response_size":4916,"timestamp":"2025-06-20T08:03:32.205003Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-4520-73c0-8314-a9e43decdc88","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:32.257831Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-4520-73c0-8314-a9e43decdc88","status_code":200,"duration_ms":4.4,"response_size":589,"timestamp":"2025-06-20T08:03:32.262150Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-4558-71a6-b463-5b7eb968f944","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:32.314003Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-4558-71a6-b463-5b7eb968f944","status_code":200,"duration_ms":4.21,"response_size":602,"timestamp":"2025-06-20T08:03:32.318115Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-458b-7266-a1c3-4aa68b416c9b","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:32.365348Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-458b-7266-a1c3-4aa68b416c9b","status_code":410,"duration_ms":4.16,"response_size":147,"timestamp":"2025-06-20T08:03:32.369425Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-45bf-7029-8239-e575fab92488","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:32.417122Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-45bf-7029-8239-e575fab92488","status_code":410,"duration_ms":3.21,"response_size":171,"timestamp":"2025-06-20T08:03:32.420242Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-45f5-7336-ba6a-c6995fb5b008","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:32.471438Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-45f5-7336-ba6a-c6995fb5b008","status_code":200,"duration_ms":3.86,"response_size":602,"timestamp":"2025-06-20T08:03:32.475169Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-4628-711c-89bf-9d39aa3cdf72","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:32.522397Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-4628-711c-89bf-9d39aa3cdf72","status_code":200,"duration_ms":4.08,"response_size":589,"timestamp":"2025-06-20T08:03:32.526371Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-465c-72e3-9449-c9e230992b82/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:32.574442Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-465c-72e3-9449-c9e230992b82/accept","status_code":422,"duration_ms":30.07,"response_size":223,"timestamp":"2025-06-20T08:03:32.604423Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-46a8-729f-8ac6-3ff75335fde9/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:32.650238Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-46a8-729f-8ac6-3ff75335fde9/accept","status_code":401,"duration_ms":2.68,"response_size":145,"timestamp":"2025-06-20T08:03:32.652832Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-46da-71eb-9bc1-ffe5bf28830c/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:32.700297Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-46da-71eb-9bc1-ffe5bf28830c/accept","status_code":200,"duration_ms":15.04,"response_size":462,"timestamp":"2025-06-20T08:03:32.715258Z"} 
[2025-06-20 08:03:32] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-471f-71f4-a255-9f9abbf7dc8f/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:32.769173Z"} 
[2025-06-20 08:03:32] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-471f-71f4-a255-9f9abbf7dc8f/accept","status_code":422,"duration_ms":2.89,"response_size":246,"timestamp":"2025-06-20T08:03:32.771925Z"} 
[2025-06-20 08:03:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:57.100637Z"} 
[2025-06-20 08:03:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":115.55,"response_size":1223,"timestamp":"2025-06-20T08:03:57.191490Z"} 
[2025-06-20 08:03:57] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"1"},"timestamp":"2025-06-20T08:03:57.296094Z"} 
[2025-06-20 08:03:57] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","status_code":200,"duration_ms":7.91,"response_size":863,"timestamp":"2025-06-20T08:03:57.303888Z"} 
[2025-06-20 08:03:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["99"],"content-type":["application/json"]},"body":{"name":"New User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1,2]},"timestamp":"2025-06-20T08:03:57.356777Z"} 
[2025-06-20 08:03:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":63.38,"response_size":446,"timestamp":"2025-06-20T08:03:57.420065Z"} 
[2025-06-20 08:03:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["76"],"content-type":["application/json"]},"body":{"name":"Simple User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T08:03:57.470821Z"} 
[2025-06-20 08:03:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":8.0,"response_size":303,"timestamp":"2025-06-20T08:03:57.478719Z"} 
[2025-06-20 08:03:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["103"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"066316"},"timestamp":"2025-06-20T08:03:57.535687Z"} 
[2025-06-20 08:03:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":201,"duration_ms":9.79,"response_size":293,"timestamp":"2025-06-20T08:03:57.545386Z"} 
[2025-06-20 08:03:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["103"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"123456"},"timestamp":"2025-06-20T08:03:57.593127Z"} 
[2025-06-20 08:03:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":12.31,"response_size":194,"timestamp":"2025-06-20T08:03:57.605387Z"} 
[2025-06-20 08:03:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["74"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T08:03:57.654103Z"} 
[2025-06-20 08:03:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":5.71,"response_size":186,"timestamp":"2025-06-20T08:03:57.659707Z"} 
[2025-06-20 08:03:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["126"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"145821","organisation_ids":[1]},"timestamp":"2025-06-20T08:03:57.708041Z"} 
[2025-06-20 08:03:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":4.78,"response_size":218,"timestamp":"2025-06-20T08:03:57.712731Z"} 
[2025-06-20 08:03:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:57.770208Z"} 
[2025-06-20 08:03:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":9.9,"response_size":305,"timestamp":"2025-06-20T08:03:57.779971Z"} 
[2025-06-20 08:03:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["77"],"content-type":["application/json"]},"body":{"name":"Test User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T08:03:57.856165Z"} 
[2025-06-20 08:03:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":10.97,"response_size":199,"timestamp":"2025-06-20T08:03:57.866744Z"} 
[2025-06-20 08:03:57] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["102"],"content-type":["application/json"]},"body":{"name":"Test User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[999,1000]},"timestamp":"2025-06-20T08:03:57.955909Z"} 
[2025-06-20 08:03:57] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":10.91,"response_size":280,"timestamp":"2025-06-20T08:03:57.966697Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:58.032026Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/3","status_code":200,"duration_ms":9.93,"response_size":481,"timestamp":"2025-06-20T08:03:58.041833Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:58.101927Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/999","status_code":404,"duration_ms":12.7,"response_size":135,"timestamp":"2025-06-20T08:03:58.114538Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["42"],"content-type":["application/json"]},"body":{"name":"New Name","organisation_ids":[2]},"timestamp":"2025-06-20T08:03:58.164740Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3","status_code":200,"duration_ms":18.33,"response_size":394,"timestamp":"2025-06-20T08:03:58.182846Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["19"],"content-type":["application/json"]},"body":{"name":"New Name"},"timestamp":"2025-06-20T08:03:58.238871Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/999","status_code":404,"duration_ms":9.48,"response_size":135,"timestamp":"2025-06-20T08:03:58.248198Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:58.321348Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":6.76,"response_size":415,"timestamp":"2025-06-20T08:03:58.327945Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:58.383207Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":6.58,"response_size":414,"timestamp":"2025-06-20T08:03:58.389648Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:58.445902Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":8.6,"response_size":422,"timestamp":"2025-06-20T08:03:58.454413Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["24"],"content-type":["application/json"]},"body":{"organisation_ids":[2]},"timestamp":"2025-06-20T08:03:58.520262Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":200,"duration_ms":8.72,"response_size":429,"timestamp":"2025-06-20T08:03:58.528861Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["23"],"content-type":["application/json"]},"body":{"organisation_ids":[]},"timestamp":"2025-06-20T08:03:58.607225Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":200,"duration_ms":10.93,"response_size":367,"timestamp":"2025-06-20T08:03:58.618056Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:58.672196Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","status_code":200,"duration_ms":6.75,"response_size":342,"timestamp":"2025-06-20T08:03:58.678828Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:58.729933Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":401,"duration_ms":3.36,"response_size":145,"timestamp":"2025-06-20T08:03:58.733209Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:58.735343Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":401,"duration_ms":1.03,"response_size":145,"timestamp":"2025-06-20T08:03:58.736396Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:58.738197Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/3","status_code":401,"duration_ms":0.9,"response_size":145,"timestamp":"2025-06-20T08:03:58.739088Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:58.740793Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3","status_code":401,"duration_ms":0.89,"response_size":145,"timestamp":"2025-06-20T08:03:58.741675Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:58.743462Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","status_code":401,"duration_ms":0.92,"response_size":145,"timestamp":"2025-06-20T08:03:58.744382Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:58.746311Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":401,"duration_ms":0.99,"response_size":145,"timestamp":"2025-06-20T08:03:58.747288Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:58.750445Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","status_code":401,"duration_ms":1.52,"response_size":145,"timestamp":"2025-06-20T08:03:58.751843Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:03:58.754624Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":401,"duration_ms":1.48,"response_size":145,"timestamp":"2025-06-20T08:03:58.756051Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T08:03:58.808602Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":200,"duration_ms":4.95,"response_size":197,"timestamp":"2025-06-20T08:03:58.813450Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["25"],"content-type":["application/json"]},"body":{"email":"invalid-email"},"timestamp":"2025-06-20T08:03:58.861962Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":12.57,"response_size":176,"timestamp":"2025-06-20T08:03:58.874396Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["32"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T08:03:58.922003Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":4.3,"response_size":181,"timestamp":"2025-06-20T08:03:58.926165Z"} 
[2025-06-20 08:03:58] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T08:03:58.976258Z"} 
[2025-06-20 08:03:58] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":5.13,"response_size":185,"timestamp":"2025-06-20T08:03:58.981312Z"} 
[2025-06-20 08:04:09] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:09.393414Z"} 
[2025-06-20 08:04:09] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":401,"duration_ms":78.22,"response_size":145,"timestamp":"2025-06-20T08:04:09.446283Z"} 
[2025-06-20 08:04:09] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:09.594186Z"} 
[2025-06-20 08:04:09] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":200,"duration_ms":28.24,"response_size":1606,"timestamp":"2025-06-20T08:04:09.622361Z"} 
[2025-06-20 08:04:09] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:09.679024Z"} 
[2025-06-20 08:04:09] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":200,"duration_ms":6.1,"response_size":664,"timestamp":"2025-06-20T08:04:09.684977Z"} 
[2025-06-20 08:04:09] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:09.740572Z"} 
[2025-06-20 08:04:09] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":403,"duration_ms":9.46,"response_size":134,"timestamp":"2025-06-20T08:04:09.749944Z"} 
[2025-06-20 08:04:09] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?role=member","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"role":"member"},"timestamp":"2025-06-20T08:04:09.820550Z"} 
[2025-06-20 08:04:09] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?role=member","status_code":200,"duration_ms":7.77,"response_size":1134,"timestamp":"2025-06-20T08:04:09.828094Z"} 
[2025-06-20 08:04:09] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?model_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"model_id":"1"},"timestamp":"2025-06-20T08:04:09.830901Z"} 
[2025-06-20 08:04:09] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?model_id=1","status_code":200,"duration_ms":6.72,"response_size":1132,"timestamp":"2025-06-20T08:04:09.837538Z"} 
[2025-06-20 08:04:09] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?model_type=App%5CModels%5COrganisation","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"model_type":"App\\Models\\Organisation"},"timestamp":"2025-06-20T08:04:09.840386Z"} 
[2025-06-20 08:04:09] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?model_type=App%5CModels%5COrganisation","status_code":200,"duration_ms":5.44,"response_size":1598,"timestamp":"2025-06-20T08:04:09.845775Z"} 
[2025-06-20 08:04:09] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=valid","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"valid"},"timestamp":"2025-06-20T08:04:09.911146Z"} 
[2025-06-20 08:04:09] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=valid","status_code":200,"duration_ms":6.4,"response_size":673,"timestamp":"2025-06-20T08:04:09.917422Z"} 
[2025-06-20 08:04:09] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=expired","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"expired"},"timestamp":"2025-06-20T08:04:09.919456Z"} 
[2025-06-20 08:04:09] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=expired","status_code":200,"duration_ms":3.94,"response_size":673,"timestamp":"2025-06-20T08:04:09.923422Z"} 
[2025-06-20 08:04:09] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=used_up","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"used_up"},"timestamp":"2025-06-20T08:04:09.925681Z"} 
[2025-06-20 08:04:09] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=used_up","status_code":200,"duration_ms":4.0,"response_size":673,"timestamp":"2025-06-20T08:04:09.929665Z"} 
[2025-06-20 08:04:10] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?per_page=10","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"per_page":"10"},"timestamp":"2025-06-20T08:04:10.007214Z"} 
[2025-06-20 08:04:10] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?per_page=10","status_code":200,"duration_ms":9.34,"response_size":4875,"timestamp":"2025-06-20T08:04:10.016462Z"} 
[2025-06-20 08:04:10] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?page=2&per_page=10","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"per_page":"10","page":"2"},"timestamp":"2025-06-20T08:04:10.018479Z"} 
[2025-06-20 08:04:10] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?page=2&per_page=10","status_code":200,"duration_ms":7.47,"response_size":4876,"timestamp":"2025-06-20T08:04:10.025951Z"} 
[2025-06-20 08:04:10] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-d8db-726e-9a75-a0750d0fda70","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:10.077340Z"} 
[2025-06-20 08:04:10] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-d8db-726e-9a75-a0750d0fda70","status_code":200,"duration_ms":5.82,"response_size":587,"timestamp":"2025-06-20T08:04:10.083008Z"} 
[2025-06-20 08:04:10] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-d919-7285-994f-a7d91bbd5177","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:10.139137Z"} 
[2025-06-20 08:04:10] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-d919-7285-994f-a7d91bbd5177","status_code":200,"duration_ms":3.8,"response_size":591,"timestamp":"2025-06-20T08:04:10.142833Z"} 
[2025-06-20 08:04:10] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-d953-70ab-8865-a1d112cc0182","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:10.196813Z"} 
[2025-06-20 08:04:10] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-d953-70ab-8865-a1d112cc0182","status_code":410,"duration_ms":4.96,"response_size":147,"timestamp":"2025-06-20T08:04:10.201689Z"} 
[2025-06-20 08:04:10] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-d98f-738b-b2af-2a48fd789d5a","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:10.256584Z"} 
[2025-06-20 08:04:10] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-d98f-738b-b2af-2a48fd789d5a","status_code":410,"duration_ms":2.8,"response_size":171,"timestamp":"2025-06-20T08:04:10.259330Z"} 
[2025-06-20 08:04:10] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-d9cf-717f-bb27-dd30ed95914a","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:10.320942Z"} 
[2025-06-20 08:04:10] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-d9cf-717f-bb27-dd30ed95914a","status_code":200,"duration_ms":4.16,"response_size":610,"timestamp":"2025-06-20T08:04:10.324974Z"} 
[2025-06-20 08:04:10] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-da06-7207-81f7-7366bd1343c5","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:10.375879Z"} 
[2025-06-20 08:04:10] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5d-da06-7207-81f7-7366bd1343c5","status_code":200,"duration_ms":4.47,"response_size":590,"timestamp":"2025-06-20T08:04:10.380258Z"} 
[2025-06-20 08:04:10] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-da3f-70c3-ad57-69e09415e9fd/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:10.433727Z"} 
[2025-06-20 08:04:10] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-da3f-70c3-ad57-69e09415e9fd/accept","status_code":422,"duration_ms":49.07,"response_size":223,"timestamp":"2025-06-20T08:04:10.482723Z"} 
[2025-06-20 08:04:10] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-daa7-728a-865e-fcfe85010367/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:10.536917Z"} 
[2025-06-20 08:04:10] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-daa7-728a-865e-fcfe85010367/accept","status_code":401,"duration_ms":3.08,"response_size":145,"timestamp":"2025-06-20T08:04:10.539896Z"} 
[2025-06-20 08:04:10] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-daf4-720a-ade3-212879ab9f12/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:10.614468Z"} 
[2025-06-20 08:04:10] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-daf4-720a-ade3-212879ab9f12/accept","status_code":200,"duration_ms":19.29,"response_size":462,"timestamp":"2025-06-20T08:04:10.633520Z"} 
[2025-06-20 08:04:10] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-db44-70f4-9075-3a284831d9ec/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:10.694768Z"} 
[2025-06-20 08:04:10] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5d-db44-70f4-9075-3a284831d9ec/accept","status_code":422,"duration_ms":3.44,"response_size":246,"timestamp":"2025-06-20T08:04:10.698164Z"} 
[2025-06-20 08:04:21] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["53"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T08:04:21.646514Z"} 
[2025-06-20 08:04:21] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":200,"duration_ms":126.83,"response_size":274,"timestamp":"2025-06-20T08:04:21.749162Z"} 
[2025-06-20 08:04:21] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["55"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T08:04:21.868173Z"} 
[2025-06-20 08:04:21] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":11.69,"response_size":199,"timestamp":"2025-06-20T08:04:21.879781Z"} 
[2025-06-20 08:04:21] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T08:04:21.935330Z"} 
[2025-06-20 08:04:21] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":36.63,"response_size":374,"timestamp":"2025-06-20T08:04:21.971794Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:22.015114Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":2.75,"response_size":145,"timestamp":"2025-06-20T08:04:22.017790Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["53"],"content-type":["application/json"]},"body":{"email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T08:04:22.064573Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":200,"duration_ms":6.02,"response_size":274,"timestamp":"2025-06-20T08:04:22.070396Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T08:04:22.072486Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":4.31,"response_size":374,"timestamp":"2025-06-20T08:04:22.076720Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/logout","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T08:04:22.079104Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/logout","status_code":200,"duration_ms":1.3,"response_size":117,"timestamp":"2025-06-20T08:04:22.080360Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T08:04:22.142106Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":6.65,"response_size":374,"timestamp":"2025-06-20T08:04:22.148672Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/revoke-all-tokens","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"],"authorization":["***FILTERED***"]},"body":[],"timestamp":"2025-06-20T08:04:22.150711Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/revoke-all-tokens","status_code":200,"duration_ms":1.3,"response_size":123,"timestamp":"2025-06-20T08:04:22.152001Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["42"],"content-type":["application/json"]},"body":{"email":"invalid-email","password":"***FILTERED***"},"timestamp":"2025-06-20T08:04:22.199878Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":13.1,"response_size":276,"timestamp":"2025-06-20T08:04:22.212869Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:22.324447Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":2.72,"response_size":145,"timestamp":"2025-06-20T08:04:22.327082Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:22.437720Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":6.85,"response_size":142,"timestamp":"2025-06-20T08:04:22.444418Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/auth/login","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["39"],"content-type":["application/json"]},"body":{"email":"invalid-email","password":null},"timestamp":"2025-06-20T08:04:22.532143Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/auth/login","status_code":422,"duration_ms":4.71,"response_size":257,"timestamp":"2025-06-20T08:04:22.536731Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:22.625299Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":401,"duration_ms":1.37,"response_size":145,"timestamp":"2025-06-20T08:04:22.626567Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:22.702896Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":401,"duration_ms":3.04,"response_size":145,"timestamp":"2025-06-20T08:04:22.705781Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:22.806363Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":200,"duration_ms":17.86,"response_size":1613,"timestamp":"2025-06-20T08:04:22.824129Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:22.879653Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":200,"duration_ms":6.37,"response_size":675,"timestamp":"2025-06-20T08:04:22.885927Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:22.935982Z"} 
[2025-06-20 08:04:22] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations","status_code":403,"duration_ms":5.93,"response_size":134,"timestamp":"2025-06-20T08:04:22.941700Z"} 
[2025-06-20 08:04:22] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?role=member","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"role":"member"},"timestamp":"2025-06-20T08:04:22.994554Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?role=member","status_code":200,"duration_ms":7.26,"response_size":1152,"timestamp":"2025-06-20T08:04:23.001623Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?model_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"model_id":"1"},"timestamp":"2025-06-20T08:04:23.003700Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?model_id=1","status_code":200,"duration_ms":4.37,"response_size":1150,"timestamp":"2025-06-20T08:04:23.008061Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?model_type=App%5CModels%5COrganisation","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"model_type":"App\\Models\\Organisation"},"timestamp":"2025-06-20T08:04:23.010154Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?model_type=App%5CModels%5COrganisation","status_code":200,"duration_ms":5.0,"response_size":1625,"timestamp":"2025-06-20T08:04:23.015150Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=valid","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"valid"},"timestamp":"2025-06-20T08:04:23.066934Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=valid","status_code":200,"duration_ms":5.59,"response_size":666,"timestamp":"2025-06-20T08:04:23.072355Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=expired","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"expired"},"timestamp":"2025-06-20T08:04:23.074011Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=expired","status_code":200,"duration_ms":3.72,"response_size":666,"timestamp":"2025-06-20T08:04:23.077732Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?status=used_up","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"used_up"},"timestamp":"2025-06-20T08:04:23.079760Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?status=used_up","status_code":200,"duration_ms":4.15,"response_size":666,"timestamp":"2025-06-20T08:04:23.083908Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?per_page=10","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"per_page":"10"},"timestamp":"2025-06-20T08:04:23.152237Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?per_page=10","status_code":200,"duration_ms":13.34,"response_size":4865,"timestamp":"2025-06-20T08:04:23.165375Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations?page=2&per_page=10","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"per_page":"10","page":"2"},"timestamp":"2025-06-20T08:04:23.167696Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations?page=2&per_page=10","status_code":200,"duration_ms":8.18,"response_size":4866,"timestamp":"2025-06-20T08:04:23.175925Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5e-0c48-727a-99a0-b1d16e18edbb","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:23.242083Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5e-0c48-727a-99a0-b1d16e18edbb","status_code":200,"duration_ms":4.82,"response_size":590,"timestamp":"2025-06-20T08:04:23.246721Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5e-0c88-702e-94a9-fbacf91f7a43","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:23.305948Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5e-0c88-702e-94a9-fbacf91f7a43","status_code":200,"duration_ms":4.09,"response_size":597,"timestamp":"2025-06-20T08:04:23.309854Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5e-0cc4-73f1-987b-8498d037d3fa","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:23.366703Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5e-0cc4-73f1-987b-8498d037d3fa","status_code":410,"duration_ms":5.39,"response_size":147,"timestamp":"2025-06-20T08:04:23.372003Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5e-0d07-7322-b89a-f501c16a8d4f","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:23.434237Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5e-0d07-7322-b89a-f501c16a8d4f","status_code":410,"duration_ms":4.04,"response_size":171,"timestamp":"2025-06-20T08:04:23.438145Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5e-0d58-7176-87af-97a9292785a0","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:23.521109Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5e-0d58-7176-87af-97a9292785a0","status_code":200,"duration_ms":5.55,"response_size":606,"timestamp":"2025-06-20T08:04:23.526362Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5e-0dbd-7176-9c3f-d64d8c11122e","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:23.615843Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/invitations/01978c5e-0dbd-7176-9c3f-d64d8c11122e","status_code":200,"duration_ms":7.52,"response_size":587,"timestamp":"2025-06-20T08:04:23.623063Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5e-0e09-726b-a80e-3b1e8ee39b70/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:23.691172Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5e-0e09-726b-a80e-3b1e8ee39b70/accept","status_code":422,"duration_ms":9.08,"response_size":223,"timestamp":"2025-06-20T08:04:23.700193Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5e-0e4e-7294-a091-309d769ea93e/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:23.759776Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5e-0e4e-7294-a091-309d769ea93e/accept","status_code":401,"duration_ms":3.26,"response_size":145,"timestamp":"2025-06-20T08:04:23.763038Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5e-0e8c-71b2-9def-fca21c96f5fc/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:23.822564Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5e-0e8c-71b2-9def-fca21c96f5fc/accept","status_code":200,"duration_ms":13.6,"response_size":462,"timestamp":"2025-06-20T08:04:23.836074Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5e-0ed7-7383-bf57-7d6efe5afa16/accept","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:23.897216Z"} 
[2025-06-20 08:04:23] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/invitations/01978c5e-0ed7-7383-bf57-7d6efe5afa16/accept","status_code":422,"duration_ms":3.92,"response_size":246,"timestamp":"2025-06-20T08:04:23.901010Z"} 
[2025-06-20 08:04:23] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:23.991686Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations","status_code":200,"duration_ms":14.09,"response_size":1800,"timestamp":"2025-06-20T08:04:24.005648Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations?status=active","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":{"status":"active"},"timestamp":"2025-06-20T08:04:24.063292Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations?status=active","status_code":200,"duration_ms":7.95,"response_size":1130,"timestamp":"2025-06-20T08:04:24.071138Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["106"],"content-type":["application/json"]},"body":{"name":"Test Organisation","code":"TEST001","details":{"industry":"Technology"},"remarks":"Test remarks"},"timestamp":"2025-06-20T08:04:24.119600Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":201,"duration_ms":8.43,"response_size":390,"timestamp":"2025-06-20T08:04:24.127905Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:24.184725Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":422,"duration_ms":7.84,"response_size":252,"timestamp":"2025-06-20T08:04:24.192472Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["47"],"content-type":["application/json"]},"body":{"name":"Test Organisation","code":"DUPLICATE"},"timestamp":"2025-06-20T08:04:24.241604Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":422,"duration_ms":8.22,"response_size":186,"timestamp":"2025-06-20T08:04:24.249752Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:24.304959Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":6.31,"response_size":475,"timestamp":"2025-06-20T08:04:24.311077Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:24.365344Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/999","status_code":404,"duration_ms":8.14,"response_size":135,"timestamp":"2025-06-20T08:04:24.373362Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["63"],"content-type":["application/json"]},"body":{"name":"New Name","code":"NEW001","remarks":"Updated remarks"},"timestamp":"2025-06-20T08:04:24.440381Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":21.05,"response_size":418,"timestamp":"2025-06-20T08:04:24.461245Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["19"],"content-type":["application/json"]},"body":{"name":"New Name"},"timestamp":"2025-06-20T08:04:24.537143Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/999","status_code":404,"duration_ms":5.23,"response_size":135,"timestamp":"2025-06-20T08:04:24.542046Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:24.600484Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":200,"duration_ms":7.18,"response_size":419,"timestamp":"2025-06-20T08:04:24.607366Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:24.677262Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":422,"duration_ms":13.17,"response_size":186,"timestamp":"2025-06-20T08:04:24.689313Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:24.775318Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations","status_code":401,"duration_ms":3.24,"response_size":145,"timestamp":"2025-06-20T08:04:24.778477Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:24.782290Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations","status_code":401,"duration_ms":1.66,"response_size":145,"timestamp":"2025-06-20T08:04:24.783905Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:24.786156Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":401,"duration_ms":1.06,"response_size":145,"timestamp":"2025-06-20T08:04:24.787203Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:24.789542Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/organisations/1","status_code":401,"duration_ms":0.87,"response_size":145,"timestamp":"2025-06-20T08:04:24.790394Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:24.792088Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/organisations/1/suspend","status_code":401,"duration_ms":0.89,"response_size":145,"timestamp":"2025-06-20T08:04:24.792972Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:24.853908Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":200,"duration_ms":7.32,"response_size":502,"timestamp":"2025-06-20T08:04:24.861000Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/2","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:24.920406Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/2","status_code":403,"duration_ms":5.36,"response_size":134,"timestamp":"2025-06-20T08:04:24.925649Z"} 
[2025-06-20 08:04:24] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:24.981962Z"} 
[2025-06-20 08:04:24] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/organisations/1","status_code":403,"duration_ms":6.64,"response_size":134,"timestamp":"2025-06-20T08:04:24.988200Z"} 
[2025-06-20 08:04:25] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:25.109480Z"} 
[2025-06-20 08:04:25] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":8.27,"response_size":417,"timestamp":"2025-06-20T08:04:25.117659Z"} 
[2025-06-20 08:04:25] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:25.179966Z"} 
[2025-06-20 08:04:25] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":5.04,"response_size":417,"timestamp":"2025-06-20T08:04:25.184872Z"} 
[2025-06-20 08:04:25] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:25.243165Z"} 
[2025-06-20 08:04:25] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":6.06,"response_size":407,"timestamp":"2025-06-20T08:04:25.249123Z"} 
[2025-06-20 08:04:25] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:25.309084Z"} 
[2025-06-20 08:04:25] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":5.32,"response_size":407,"timestamp":"2025-06-20T08:04:25.314314Z"} 
[2025-06-20 08:04:25] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:25.372605Z"} 
[2025-06-20 08:04:25] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":3.14,"response_size":142,"timestamp":"2025-06-20T08:04:25.375626Z"} 
[2025-06-20 08:04:25] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:25.438469Z"} 
[2025-06-20 08:04:25] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":3.37,"response_size":142,"timestamp":"2025-06-20T08:04:25.441737Z"} 
[2025-06-20 08:04:25] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:25.507467Z"} 
[2025-06-20 08:04:25] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":401,"duration_ms":2.93,"response_size":145,"timestamp":"2025-06-20T08:04:25.510311Z"} 
[2025-06-20 08:04:25] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:25.580247Z"} 
[2025-06-20 08:04:25] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":3.92,"response_size":142,"timestamp":"2025-06-20T08:04:25.584069Z"} 
[2025-06-20 08:04:25] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:25.655976Z"} 
[2025-06-20 08:04:25] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":5.31,"response_size":417,"timestamp":"2025-06-20T08:04:25.661045Z"} 
[2025-06-20 08:04:25] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:25.729713Z"} 
[2025-06-20 08:04:25] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":6.92,"response_size":417,"timestamp":"2025-06-20T08:04:25.736517Z"} 
[2025-06-20 08:04:25] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:25.823041Z"} 
[2025-06-20 08:04:25] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":4.18,"response_size":142,"timestamp":"2025-06-20T08:04:25.827114Z"} 
[2025-06-20 08:04:25] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:25.901465Z"} 
[2025-06-20 08:04:25] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":6.2,"response_size":407,"timestamp":"2025-06-20T08:04:25.907543Z"} 
[2025-06-20 08:04:25] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["59"],"content-type":["application/json"]},"body":{"name":"test-role","organisation_id":1,"guard_name":"api"},"timestamp":"2025-06-20T08:04:25.973337Z"} 
[2025-06-20 08:04:25] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":201,"duration_ms":6.94,"response_size":256,"timestamp":"2025-06-20T08:04:25.980169Z"} 
[2025-06-20 08:04:26] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["49"],"content-type":["application/json"]},"body":{"name":"test-system-role","guard_name":"system"},"timestamp":"2025-06-20T08:04:26.044547Z"} 
[2025-06-20 08:04:26] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":201,"duration_ms":7.73,"response_size":269,"timestamp":"2025-06-20T08:04:26.052170Z"} 
[2025-06-20 08:04:26] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["65"],"content-type":["application/json"]},"body":{"name":"invalid-role","organisation_id":1,"guard_name":"system"},"timestamp":"2025-06-20T08:04:26.117202Z"} 
[2025-06-20 08:04:26] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/roles","status_code":422,"duration_ms":5.94,"response_size":114,"timestamp":"2025-06-20T08:04:26.123018Z"} 
[2025-06-20 08:04:26] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:26.187090Z"} 
[2025-06-20 08:04:26] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles/3","status_code":200,"duration_ms":4.65,"response_size":254,"timestamp":"2025-06-20T08:04:26.191612Z"} 
[2025-06-20 08:04:26] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:26.256002Z"} 
[2025-06-20 08:04:26] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":401,"duration_ms":2.77,"response_size":145,"timestamp":"2025-06-20T08:04:26.258568Z"} 
[2025-06-20 08:04:26] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:26.329206Z"} 
[2025-06-20 08:04:26] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":200,"duration_ms":9.58,"response_size":417,"timestamp":"2025-06-20T08:04:26.338669Z"} 
[2025-06-20 08:04:26] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:26.432091Z"} 
[2025-06-20 08:04:26] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/roles","status_code":403,"duration_ms":3.81,"response_size":142,"timestamp":"2025-06-20T08:04:26.435565Z"} 
[2025-06-20 08:04:26] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:26.541751Z"} 
[2025-06-20 08:04:26] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":16.01,"response_size":2217,"timestamp":"2025-06-20T08:04:26.557640Z"} 
[2025-06-20 08:04:26] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:26.633289Z"} 
[2025-06-20 08:04:26] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":7.86,"response_size":2206,"timestamp":"2025-06-20T08:04:26.641028Z"} 
[2025-06-20 08:04:26] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:26.700320Z"} 
[2025-06-20 08:04:26] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":6.57,"response_size":794,"timestamp":"2025-06-20T08:04:26.706829Z"} 
[2025-06-20 08:04:26] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"1"},"timestamp":"2025-06-20T08:04:26.769255Z"} 
[2025-06-20 08:04:26] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","status_code":200,"duration_ms":7.51,"response_size":800,"timestamp":"2025-06-20T08:04:26.776767Z"} 
[2025-06-20 08:04:26] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=2","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"2"},"timestamp":"2025-06-20T08:04:26.840294Z"} 
[2025-06-20 08:04:26] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=2","status_code":422,"duration_ms":5.08,"response_size":197,"timestamp":"2025-06-20T08:04:26.845286Z"} 
[2025-06-20 08:04:26] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:26.910049Z"} 
[2025-06-20 08:04:26] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":9.05,"response_size":790,"timestamp":"2025-06-20T08:04:26.918905Z"} 
[2025-06-20 08:04:27] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:27.002574Z"} 
[2025-06-20 08:04:27] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":403,"duration_ms":4.8,"response_size":134,"timestamp":"2025-06-20T08:04:27.007170Z"} 
[2025-06-20 08:04:27] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:27.087383Z"} 
[2025-06-20 08:04:27] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":6.09,"response_size":414,"timestamp":"2025-06-20T08:04:27.093328Z"} 
[2025-06-20 08:04:27] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:27.251224Z"} 
[2025-06-20 08:04:27] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":6.12,"response_size":413,"timestamp":"2025-06-20T08:04:27.257198Z"} 
[2025-06-20 08:04:27] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/7","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:27.329182Z"} 
[2025-06-20 08:04:27] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/7","status_code":403,"duration_ms":4.41,"response_size":134,"timestamp":"2025-06-20T08:04:27.333480Z"} 
[2025-06-20 08:04:27] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/4","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:27.404888Z"} 
[2025-06-20 08:04:27] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/4","status_code":200,"duration_ms":7.64,"response_size":418,"timestamp":"2025-06-20T08:04:27.412327Z"} 
[2025-06-20 08:04:27] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:27.489384Z"} 
[2025-06-20 08:04:27] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/6","status_code":403,"duration_ms":4.55,"response_size":134,"timestamp":"2025-06-20T08:04:27.493848Z"} 
[2025-06-20 08:04:27] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["99"],"content-type":["application/json"]},"body":{"name":"New User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1,2]},"timestamp":"2025-06-20T08:04:27.562200Z"} 
[2025-06-20 08:04:27] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":10.44,"response_size":435,"timestamp":"2025-06-20T08:04:27.572543Z"} 
[2025-06-20 08:04:27] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["104"],"content-type":["application/json"]},"body":{"name":"New Org User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1]},"timestamp":"2025-06-20T08:04:27.666655Z"} 
[2025-06-20 08:04:27] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":12.75,"response_size":374,"timestamp":"2025-06-20T08:04:27.679247Z"} 
[2025-06-20 08:04:27] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["101"],"content-type":["application/json"]},"body":{"name":"Invalid User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[2]},"timestamp":"2025-06-20T08:04:27.761504Z"} 
[2025-06-20 08:04:27] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":7.63,"response_size":215,"timestamp":"2025-06-20T08:04:27.769014Z"} 
[2025-06-20 08:04:27] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["85"],"content-type":["application/json"]},"body":{"name":"Default Org User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T08:04:27.838055Z"} 
[2025-06-20 08:04:27] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":8.12,"response_size":381,"timestamp":"2025-06-20T08:04:27.845964Z"} 
[2025-06-20 08:04:27] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["88"],"content-type":["application/json"]},"body":{"name":"Unauthorized User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T08:04:27.907178Z"} 
[2025-06-20 08:04:27] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":403,"duration_ms":3.51,"response_size":134,"timestamp":"2025-06-20T08:04:27.910581Z"} 
[2025-06-20 08:04:27] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["23"],"content-type":["application/json"]},"body":{"name":"Updated Name"},"timestamp":"2025-06-20T08:04:27.971557Z"} 
[2025-06-20 08:04:27] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":7.56,"response_size":396,"timestamp":"2025-06-20T08:04:27.979011Z"} 
[2025-06-20 08:04:28] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["30"],"content-type":["application/json"]},"body":{"name":"Updated Member Name"},"timestamp":"2025-06-20T08:04:28.040231Z"} 
[2025-06-20 08:04:28] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":200,"duration_ms":7.08,"response_size":404,"timestamp":"2025-06-20T08:04:28.047228Z"} 
[2025-06-20 08:04:28] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/7","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"name":"Should Not Update"},"timestamp":"2025-06-20T08:04:28.106660Z"} 
[2025-06-20 08:04:28] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/7","status_code":403,"duration_ms":4.38,"response_size":134,"timestamp":"2025-06-20T08:04:28.110958Z"} 
[2025-06-20 08:04:28] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["30"],"content-type":["application/json"]},"body":{"name":"Unauthorized Update"},"timestamp":"2025-06-20T08:04:28.171732Z"} 
[2025-06-20 08:04:28] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6","status_code":403,"duration_ms":4.13,"response_size":134,"timestamp":"2025-06-20T08:04:28.175749Z"} 
[2025-06-20 08:04:28] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:28.236690Z"} 
[2025-06-20 08:04:28] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":200,"duration_ms":5.06,"response_size":407,"timestamp":"2025-06-20T08:04:28.241649Z"} 
[2025-06-20 08:04:28] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:28.302474Z"} 
[2025-06-20 08:04:28] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":200,"duration_ms":5.48,"response_size":398,"timestamp":"2025-06-20T08:04:28.307836Z"} 
[2025-06-20 08:04:28] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/7/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:28.368796Z"} 
[2025-06-20 08:04:28] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/7/suspend","status_code":403,"duration_ms":4.63,"response_size":134,"timestamp":"2025-06-20T08:04:28.373324Z"} 
[2025-06-20 08:04:28] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:28.443704Z"} 
[2025-06-20 08:04:28] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":200,"duration_ms":5.87,"response_size":397,"timestamp":"2025-06-20T08:04:28.449459Z"} 
[2025-06-20 08:04:28] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:28.519169Z"} 
[2025-06-20 08:04:28] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":200,"duration_ms":20.42,"response_size":401,"timestamp":"2025-06-20T08:04:28.539477Z"} 
[2025-06-20 08:04:28] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/7/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:28.654459Z"} 
[2025-06-20 08:04:28] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/7/activate","status_code":403,"duration_ms":4.28,"response_size":134,"timestamp":"2025-06-20T08:04:28.658621Z"} 
[2025-06-20 08:04:28] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:28.719043Z"} 
[2025-06-20 08:04:28] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/suspend","status_code":403,"duration_ms":4.57,"response_size":134,"timestamp":"2025-06-20T08:04:28.723400Z"} 
[2025-06-20 08:04:28] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/activate","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:28.787137Z"} 
[2025-06-20 08:04:28] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/activate","status_code":403,"duration_ms":4.26,"response_size":134,"timestamp":"2025-06-20T08:04:28.791276Z"} 
[2025-06-20 08:04:28] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:28.892742Z"} 
[2025-06-20 08:04:28] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":200,"duration_ms":10.09,"response_size":1230,"timestamp":"2025-06-20T08:04:28.902740Z"} 
[2025-06-20 08:04:28] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":{"organisation_id":"1"},"timestamp":"2025-06-20T08:04:28.965766Z"} 
[2025-06-20 08:04:28] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users?organisation_id=1","status_code":200,"duration_ms":8.58,"response_size":861,"timestamp":"2025-06-20T08:04:28.974224Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["99"],"content-type":["application/json"]},"body":{"name":"New User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[1,2]},"timestamp":"2025-06-20T08:04:29.025891Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":10.83,"response_size":446,"timestamp":"2025-06-20T08:04:29.036581Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["76"],"content-type":["application/json"]},"body":{"name":"Simple User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T08:04:29.088593Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":201,"duration_ms":8.35,"response_size":303,"timestamp":"2025-06-20T08:04:29.096839Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["103"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"255528"},"timestamp":"2025-06-20T08:04:29.151997Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":201,"duration_ms":9.04,"response_size":293,"timestamp":"2025-06-20T08:04:29.160955Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["103"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"123456"},"timestamp":"2025-06-20T08:04:29.210966Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":5.51,"response_size":194,"timestamp":"2025-06-20T08:04:29.216305Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["74"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T08:04:29.264697Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":6.85,"response_size":186,"timestamp":"2025-06-20T08:04:29.271435Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/register","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["126"],"content-type":["application/json"]},"body":{"name":"Guest User","email":"<EMAIL>","password":"***FILTERED***","verification_code":"439834","organisation_ids":[1]},"timestamp":"2025-06-20T08:04:29.323099Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/register","status_code":422,"duration_ms":4.34,"response_size":218,"timestamp":"2025-06-20T08:04:29.327363Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:29.385420Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":9.49,"response_size":305,"timestamp":"2025-06-20T08:04:29.394840Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["77"],"content-type":["application/json"]},"body":{"name":"Test User","email":"<EMAIL>","password":"***FILTERED***"},"timestamp":"2025-06-20T08:04:29.463430Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":11.76,"response_size":199,"timestamp":"2025-06-20T08:04:29.474976Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["102"],"content-type":["application/json"]},"body":{"name":"Test User","email":"<EMAIL>","password":"***FILTERED***","organisation_ids":[999,1000]},"timestamp":"2025-06-20T08:04:29.552244Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":422,"duration_ms":9.34,"response_size":280,"timestamp":"2025-06-20T08:04:29.561473Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:29.616578Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/3","status_code":200,"duration_ms":7.13,"response_size":481,"timestamp":"2025-06-20T08:04:29.623578Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:29.676402Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/999","status_code":404,"duration_ms":5.4,"response_size":135,"timestamp":"2025-06-20T08:04:29.681861Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["42"],"content-type":["application/json"]},"body":{"name":"New Name","organisation_ids":[2]},"timestamp":"2025-06-20T08:04:29.740681Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3","status_code":200,"duration_ms":12.61,"response_size":394,"timestamp":"2025-06-20T08:04:29.753200Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/999","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["19"],"content-type":["application/json"]},"body":{"name":"New Name"},"timestamp":"2025-06-20T08:04:29.808510Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/999","status_code":404,"duration_ms":5.01,"response_size":135,"timestamp":"2025-06-20T08:04:29.813446Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:29.871398Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":7.03,"response_size":420,"timestamp":"2025-06-20T08:04:29.878303Z"} 
[2025-06-20 08:04:29] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:29.936631Z"} 
[2025-06-20 08:04:29] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":6.54,"response_size":418,"timestamp":"2025-06-20T08:04:29.943022Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:30.002979Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","status_code":200,"duration_ms":8.63,"response_size":426,"timestamp":"2025-06-20T08:04:30.011479Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["24"],"content-type":["application/json"]},"body":{"organisation_ids":[2]},"timestamp":"2025-06-20T08:04:30.068620Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":200,"duration_ms":9.89,"response_size":429,"timestamp":"2025-06-20T08:04:30.078415Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["23"],"content-type":["application/json"]},"body":{"organisation_ids":[]},"timestamp":"2025-06-20T08:04:30.136236Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":200,"duration_ms":10.64,"response_size":359,"timestamp":"2025-06-20T08:04:30.146803Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"authorization":["***FILTERED***"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:30.204580Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","status_code":200,"duration_ms":7.33,"response_size":329,"timestamp":"2025-06-20T08:04:30.211715Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:30.274038Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users","status_code":401,"duration_ms":3.25,"response_size":145,"timestamp":"2025-06-20T08:04:30.277190Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:30.279563Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users","status_code":401,"duration_ms":0.97,"response_size":145,"timestamp":"2025-06-20T08:04:30.280513Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:30.283065Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/3","status_code":401,"duration_ms":1.32,"response_size":145,"timestamp":"2025-06-20T08:04:30.284416Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:30.286772Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3","status_code":401,"duration_ms":1.27,"response_size":145,"timestamp":"2025-06-20T08:04:30.288009Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:30.290051Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/suspend","status_code":401,"duration_ms":1.08,"response_size":145,"timestamp":"2025-06-20T08:04:30.291133Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:30.293481Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/3/organisations/1","status_code":401,"duration_ms":1.12,"response_size":145,"timestamp":"2025-06-20T08:04:30.294591Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:30.296679Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"DELETE","url":"http://localhost/api/v1/users/3/organisations/1","status_code":401,"duration_ms":1.04,"response_size":145,"timestamp":"2025-06-20T08:04:30.297722Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:30.300240Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/3/organisations","status_code":401,"duration_ms":1.21,"response_size":145,"timestamp":"2025-06-20T08:04:30.301426Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T08:04:30.353222Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":200,"duration_ms":5.28,"response_size":197,"timestamp":"2025-06-20T08:04:30.358405Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["25"],"content-type":["application/json"]},"body":{"email":"invalid-email"},"timestamp":"2025-06-20T08:04:30.409412Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":5.84,"response_size":176,"timestamp":"2025-06-20T08:04:30.415172Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["32"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T08:04:30.468081Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":4.8,"response_size":181,"timestamp":"2025-06-20T08:04:30.472825Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["28"],"content-type":["application/json"]},"body":{"email":"<EMAIL>"},"timestamp":"2025-06-20T08:04:30.542012Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/send-verification-code","status_code":422,"duration_ms":3.45,"response_size":185,"timestamp":"2025-06-20T08:04:30.545304Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T08:04:30.669029Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":8.99,"response_size":180,"timestamp":"2025-06-20T08:04:30.677787Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T08:04:30.737198Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":6.25,"response_size":180,"timestamp":"2025-06-20T08:04:30.743348Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T08:04:30.797661Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":200,"duration_ms":7.86,"response_size":180,"timestamp":"2025-06-20T08:04:30.805397Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":3},"timestamp":"2025-06-20T08:04:30.859842Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":422,"duration_ms":6.6,"response_size":263,"timestamp":"2025-06-20T08:04:30.866255Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":4},"timestamp":"2025-06-20T08:04:30.920065Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":403,"duration_ms":6.45,"response_size":134,"timestamp":"2025-06-20T08:04:30.926414Z"} 
[2025-06-20 08:04:30] testing.INFO: API Request {"method":"POST","url":"http://localhost/api/v1/users/6/roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["13"],"content-type":["application/json"]},"body":{"role_id":3},"timestamp":"2025-06-20T08:04:30.985285Z"} 
[2025-06-20 08:04:30] testing.INFO: API Response {"method":"POST","url":"http://localhost/api/v1/users/6/roles","status_code":422,"duration_ms":5.93,"response_size":263,"timestamp":"2025-06-20T08:04:30.991098Z"} 
[2025-06-20 08:04:31] testing.INFO: API Request {"method":"PUT","url":"http://localhost/api/v1/users/6/transfer-owner","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["21"],"content-type":["application/json"]},"body":{"organisation_id":1},"timestamp":"2025-06-20T08:04:31.046674Z"} 
[2025-06-20 08:04:31] testing.INFO: API Response {"method":"PUT","url":"http://localhost/api/v1/users/6/transfer-owner","status_code":200,"duration_ms":9.55,"response_size":193,"timestamp":"2025-06-20T08:04:31.056124Z"} 
[2025-06-20 08:04:31] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/user","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:31.120372Z"} 
[2025-06-20 08:04:31] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/user","status_code":200,"duration_ms":6.48,"response_size":550,"timestamp":"2025-06-20T08:04:31.126721Z"} 
[2025-06-20 08:04:31] testing.INFO: API Request {"method":"GET","url":"http://localhost/api/v1/users/assignable-roles","ip":"127.0.0.1","user_agent":"Symfony","headers":{"host":["localhost"],"user-agent":["Symfony"],"accept":["application/json"],"accept-language":["en-us,en;q=0.5"],"accept-charset":["ISO-8859-1,utf-8;q=0.7,*;q=0.7"],"content-length":["2"],"content-type":["application/json"]},"body":[],"timestamp":"2025-06-20T08:04:31.195023Z"} 
[2025-06-20 08:04:31] testing.INFO: API Response {"method":"GET","url":"http://localhost/api/v1/users/assignable-roles","status_code":200,"duration_ms":8.79,"response_size":164,"timestamp":"2025-06-20T08:04:31.203593Z"} 
