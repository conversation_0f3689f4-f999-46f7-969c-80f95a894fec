# JAST Partner API 文档

## 概述

JAST Partner API 是一个基于 Laravel 12 和 PHP 8.4 构建的 RESTful API，提供用户认证、健康检查等核心功能。

## 基础信息

- **基础URL**: `http://localhost` (开发环境)
- **API版本**: v1
- **认证方式**: <PERSON><PERSON> (Laravel Sanctum)
- **数据格式**: JSON

## 环境配置

### 开发环境
- **URL**: `http://localhost`
- **端口**: 80 (HTTP), 443 (HTTPS)

### 生产环境
- **URL**: `https://your-production-domain.com`

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 响应数据
  },
  "timestamp": "2025-06-05T11:10:47.593773Z"
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "errors": {
    // 详细错误信息
  },
  "timestamp": "2025-06-05T11:10:47.593773Z"
}
```

## API 端点

### 1. 健康检查和状态

#### 1.1 全局健康检查
- **端点**: `GET /api/health`
- **描述**: 检查API服务整体健康状态
- **认证**: 无需认证
- **响应示例**:
```json
{
  "status": "healthy",
  "checks": {
    "database": {
      "status": "ok",
      "message": "Database connection successful"
    },
    "cache": {
      "status": "ok", 
      "message": "Cache check completed"
    },
    "storage": {
      "status": "ok",
      "message": "Storage check completed"
    }
  },
  "timestamp": "2025-06-05T11:09:28.385058Z"
}
```

#### 1.2 API v1 健康检查
- **端点**: `GET /api/v1/health`
- **描述**: 检查API v1版本健康状态
- **认证**: 无需认证

#### 1.3 API v1 状态信息
- **端点**: `GET /api/v1/status`
- **描述**: 获取API v1详细状态信息
- **认证**: 无需认证

### 2. 用户认证与注册

#### 2.1 发送邮箱验证码
- **端点**: `POST /api/v1/users/send-verification-code`
- **描述**: 为用户注册发送邮箱验证码
- **认证**: 无需认证
- **请求体**:
```json
{
  "email": "<EMAIL>"
}
```
- **验证规则**:
  - `email`: 必填，有效邮箱格式，最大255字符
- **成功响应**:
```json
{
  "success": true,
  "message": "Verification code sent to your email address",
  "data": {
    "message": "Verification code sent successfully",
    "expires_in_seconds": 900
  },
  "timestamp": "2025-06-20T12:00:00.000000Z"
}
```
- **错误响应**:
  - **422 验证失败**: 验证码已发送，需要等待
  ```json
  {
    "success": false,
    "message": "A verification code has already been sent to this email address. Please wait before requesting a new one.",
    "errors": {
      "retry_after_seconds": 600
    },
    "timestamp": "2025-06-20T12:00:00.000000Z"
  }
  ```

#### 2.2 用户注册
- **端点**: `POST /api/v1/users/register`
- **描述**: 游客用户注册（无需认证）
- **认证**: 无需认证
- **请求体**:
```json
{
  "name": "New User",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123",
  "verification_code": "123456",
  "organisation_ids": [1, 2]
}
```
- **验证规则**:
  - `name`: 必填，字符串，最大255字符
  - `email`: 必填，有效邮箱格式，最大255字符，必须唯一
  - `password`: 必填，字符串，最少8个字符
  - `password_confirmation`: 必填，必须与password匹配
  - `verification_code`: 必填，6位数字验证码
  - `organisation_ids`: 可选，组织ID数组
- **成功响应**:
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "id": 2,
    "name": "New User",
    "email": "<EMAIL>",
    "email_verified_at": "2025-06-20T12:00:00.000000Z",
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      }
    ],
    "created_at": "2025-06-20T12:00:00.000000Z",
    "updated_at": "2025-06-20T12:00:00.000000Z"
  },
  "timestamp": "2025-06-20T12:00:00.000000Z"
}
```

#### 2.3 用户登录
- **端点**: `POST /api/v1/auth/login`
- **描述**: 用户登录获取访问令牌
- **认证**: 无需认证
- **请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```
- **验证规则**:
  - `email`: 必填，有效邮箱格式，最大255字符
  - `password`: 必填，字符串，最少8个字符
- **成功响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "id": 1,
      "name": "Test User",
      "email": "<EMAIL>",
      "email_verified_at": null
    },
    "token": "3|7JHCY3kjoLZPlvGUGjhDZLrRXMnuH88o0WAvUNjc33285326",
    "token_type": "Bearer"
  },
  "timestamp": "2025-06-05T11:10:47.593773Z"
}
```

#### 2.4 用户退出登录
- **端点**: `POST /api/v1/auth/logout`
- **描述**: 撤销当前访问令牌
- **认证**: 需要Bearer Token
- **响应示例**:
```json
{
  "success": true,
  "message": "退出登录成功",
  "data": [],
  "timestamp": "2025-06-05T11:12:36.228620Z"
}
```

#### 2.5 撤销所有令牌
- **端点**: `POST /api/v1/auth/revoke-all-tokens`
- **描述**: 撤销用户的所有访问令牌
- **认证**: 需要Bearer Token
- **响应示例**:
```json
{
  "success": true,
  "message": "所有令牌已撤销",
  "data": [],
  "timestamp": "2025-06-05T11:12:36.228620Z"
}
```

### 3. 用户管理

#### 3.1 获取当前用户
- **端点**: `GET /api/v1/user`
- **描述**: 获取当前认证用户的基本信息
- **认证**: 需要Bearer Token
- **响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Test User",
    "email": "<EMAIL>",
    "email_verified_at": null,
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      },
      {
        "id": 2,
        "name": "Innovation Corp",
        "code": "INNO002",
        "status": "active"
      }
    ],
    "roles": {
      "system_roles": [
        {
          "id": 1,
          "name": "root",
          "guard_name": "system",
          "organisation_id": null
        }
      ],
      "organisation_roles": [
        {
          "id": 3,
          "name": "owner",
          "guard_name": "api",
          "organisation_id": 1,
          "organisation_name": "Tech Solutions Ltd"
        }
      ],
      "all_role_names": ["root", "owner"]
    },
    "created_at": "2025-06-05T11:08:36.000000Z",
    "updated_at": "2025-06-05T11:08:36.000000Z"
  },
  "timestamp": "2025-06-05T11:11:01.047925Z"
}
```

#### 3.2 获取用户列表
- **端点**: `GET /api/v1/users`
- **描述**: 获取用户列表，支持分页和组织筛选
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以查看所有用户
  - **组织所有者（Owner）**: 只能查看自己组织的用户
  - **组织成员（Member）**: 只能查看自己组织的用户
- **查询参数**:
  - `per_page` (可选): 每页数量，默认15
  - `organisation_id` (可选): 按组织ID筛选用户（组织用户只能筛选自己的组织）
  - `status` (可选): 按状态筛选用户
- **响应示例**:
```json
{
  "success": true,
  "message": "获取用户列表成功",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "email_verified_at": null,
        "organisations": [
          {
            "id": 1,
            "name": "Tech Solutions Ltd",
            "code": "TECH001",
            "status": "active"
          }
        ],
        "created_at": "2025-06-06T06:56:33.000000Z",
        "updated_at": "2025-06-06T06:56:33.000000Z"
      }
    ],
    "meta": {
      "total": 1,
      "per_page": 15,
      "current_page": 1,
      "last_page": 1,
      "from": 1,
      "to": 1
    }
  },
  "timestamp": "2025-06-06T06:56:33.228620Z"
}
```

#### 3.3 创建用户
- **端点**: `POST /api/v1/users`
- **描述**: 创建新用户
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以在任何组织中创建用户
  - **组织所有者（Owner）**: 只能在自己的组织中创建用户
- **请求参数**:
  - `name` (必填): 用户名称
  - `email` (必填): 邮箱地址，必须唯一
  - `password` (必填): 密码，最少8个字符
  - `organisation_ids` (可选): 组织ID数组（组织所有者只能指定自己的组织）
- **请求示例**:
```json
{
  "name": "New User",
  "email": "<EMAIL>",
  "password": "password123",
  "organisation_ids": [1, 2]
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "用户创建成功",
  "data": {
    "id": 2,
    "name": "New User",
    "email": "<EMAIL>",
    "email_verified_at": null,
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      },
      {
        "id": 2,
        "name": "Innovation Corp",
        "code": "INNO002",
        "status": "active"
      }
    ],
    "created_at": "2025-06-06T07:00:00.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 3.4 获取用户详情
- **端点**: `GET /api/v1/users/{id}`
- **描述**: 获取指定用户的详细信息
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以查看任何用户
  - **组织用户（Owner/Member）**: 只能查看同组织的用户
- **响应示例**:
```json
{
  "success": true,
  "message": "获取用户详情成功",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "email_verified_at": null,
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      }
    ],
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T06:56:33.000000Z"
  },
  "timestamp": "2025-06-06T06:56:33.228620Z"
}
```

#### 3.5 更新用户
- **端点**: `PUT /api/v1/users/{id}`
- **描述**: 更新用户信息
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以更新任何用户
  - **组织所有者（Owner）**: 只能更新同组织的用户
- **请求参数**: 所有参数都是可选的
  - `name`: 用户名称
  - `email`: 邮箱地址，必须唯一
  - `password`: 密码，最少8个字符
  - `organisation_ids`: 组织ID数组（组织所有者只能指定自己的组织）
- **请求示例**:
```json
{
  "name": "Updated Name",
  "organisation_ids": [2, 3]
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "用户更新成功",
  "data": {
    "id": 1,
    "name": "Updated Name",
    "email": "<EMAIL>",
    "email_verified_at": null,
    "organisations": [
      {
        "id": 2,
        "name": "Innovation Corp",
        "code": "INNO002",
        "status": "active"
      },
      {
        "id": 3,
        "name": "Future Tech",
        "code": "FUTURE003",
        "status": "active"
      }
    ],
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 3.6 暂停用户
- **端点**: `POST /api/v1/users/{id}/suspend`
- **描述**: 暂停用户（撤销所有访问令牌）
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以暂停任何用户
  - **组织所有者（Owner）**: 只能暂停同组织的用户
- **响应示例**:
```json
{
  "success": true,
  "message": "用户暂停成功",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "organisations": [],
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 3.7 激活用户
- **端点**: `POST /api/v1/users/{id}/activate`
- **描述**: 激活用户
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以激活任何用户
  - **组织所有者（Owner）**: 只能激活同组织的用户
- **响应示例**:
```json
{
  "success": true,
  "message": "用户激活成功",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      }
    ],
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

### 3.8 用户-组织关联管理

#### 3.8.1 添加用户到组织
- **端点**: `POST /api/v1/users/{userId}/organisations/{organisationId}`
- **描述**: 将用户添加到指定组织
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以将用户添加到任何组织
  - **组织所有者（Owner）**: 只能将用户添加到自己拥有的组织
- **响应示例**:
```json
{
  "success": true,
  "message": "用户已添加到组织",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      },
      {
        "id": 2,
        "name": "Innovation Corp",
        "code": "INNO002",
        "status": "active"
      }
    ],
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 3.8.2 从组织中移除用户
- **端点**: `DELETE /api/v1/users/{userId}/organisations/{organisationId}`
- **描述**: 从指定组织中移除用户
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以从任何组织中移除用户
  - **组织所有者（Owner）**: 只能从自己拥有的组织中移除用户
- **响应示例**:
```json
{
  "success": true,
  "message": "用户已从组织中移除",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      }
    ],
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 3.8.3 同步用户组织关联
- **端点**: `PUT /api/v1/users/{userId}/organisations`
- **描述**: 同步用户的组织关联（替换现有关联）
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以同步用户到任何组织
  - **组织所有者（Owner）**: 只能同步用户到自己拥有的组织
- **请求参数**:
  - `organisation_ids` (必填): 组织ID数组（组织所有者只能指定自己拥有的组织）
- **请求示例**:
```json
{
  "organisation_ids": [1, 3]
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "用户组织关联已同步",
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      },
      {
        "id": 3,
        "name": "Future Tech",
        "code": "FUTURE003",
        "status": "active"
      }
    ],
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

### 4. 用户角色管理

#### 4.1 获取可分配角色列表
- **端点**: `GET /api/v1/users/assignable-roles`
- **描述**: 获取当前用户可以分配给其他用户的角色列表
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以分配除Root外的所有角色
  - **组织所有者（Owner）**: 只能分配Member角色
- **响应示例**:
```json
{
  "success": true,
  "message": "可赋予角色列表获取成功",
  "data": [
    {
      "id": 4,
      "name": "member",
      "guard_name": "api",
      "organisation_id": 1,
      "created_at": "2025-06-06T06:56:33.000000Z",
      "updated_at": "2025-06-06T06:56:33.000000Z"
    }
  ],
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 4.2 为用户分配角色
- **端点**: `POST /api/v1/users/{user}/roles`
- **描述**: 为指定用户分配角色
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以分配除Root外的所有角色
  - **组织所有者（Owner）**: 只能在自己的组织内分配Member角色
- **请求参数**:
  - `role_name` (必填): 角色名称
  - `organisation_id` (可选): 组织ID（组织级角色必填）
- **请求示例**:
```json
{
  "role_name": "member",
  "organisation_id": 1
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "角色分配成功",
  "data": {
    "user_id": 2,
    "role_name": "member",
    "organisation_id": 1
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 4.3 移除用户角色
- **端点**: `DELETE /api/v1/users/{user}/roles/{role}`
- **描述**: 移除用户的指定角色
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以移除除Root外的所有角色
  - **组织所有者（Owner）**: 只能移除自己组织内的Member角色
- **响应示例**:
```json
{
  "success": true,
  "message": "角色移除成功",
  "data": {
    "user_id": 2,
    "role_id": 4,
    "role_name": "member"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 4.4 获取用户角色信息
- **端点**: `GET /api/v1/users/{user}/roles`
- **描述**: 获取指定用户的详细角色信息
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以查看任何用户的角色
  - **组织用户（Owner/Member）**: 只能查看同组织用户的角色
- **响应示例**:
```json
{
  "success": true,
  "message": "用户角色信息获取成功",
  "data": {
    "user_id": 2,
    "user_name": "John Doe",
    "user_email": "<EMAIL>",
    "organisations": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "status": "active"
      }
    ],
    "roles": {
      "system_roles": [],
      "organisation_roles": [
        {
          "id": 4,
          "name": "member",
          "guard_name": "api",
          "organisation_id": 1,
          "organisation_name": "Tech Solutions Ltd"
        }
      ],
      "all_role_names": ["member"]
    }
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 4.5 转移所有者角色
- **端点**: `PUT /api/v1/users/{user}/transfer-owner`
- **描述**: 将组织所有者角色转移给指定用户
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以转移任何组织的所有者角色
  - **组织所有者（Owner）**: 只能转移自己拥有的组织的所有者角色
- **请求参数**:
  - `organisation_id` (必填): 要转移所有者角色的组织ID
- **请求示例**:
```json
{
  "organisation_id": 1
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "所有者角色转移成功",
  "data": {
    "new_owner": {
      "id": 2,
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "organisation": {
      "id": 1,
      "name": "Tech Solutions Ltd",
      "code": "TECH001"
    },
    "previous_owner": {
      "id": 1,
      "name": "Previous Owner",
      "email": "<EMAIL>"
    }
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

### 5. 组织管理

#### 5.1 获取组织列表
- **端点**: `GET /api/v1/organisations`
- **描述**: 获取组织列表，支持分页和状态筛选
- **认证**: 需要Bearer Token
- **查询参数**:
  - `per_page` (可选): 每页数量，默认15
  - `status` (可选): 状态筛选 (pending, active, suspended)
- **响应示例**:
```json
{
  "success": true,
  "message": "获取组织列表成功",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "Tech Solutions Ltd",
        "code": "TECH001",
        "details": {
          "industry": "Technology",
          "size": "Medium",
          "founded": 2020
        },
        "remarks": "Leading technology solutions provider",
        "status": "active",
        "is_active": true,
        "is_pending": false,
        "is_suspended": false,
        "users_count": 5,
        "created_at": "2025-06-06T06:56:33.000000Z",
        "updated_at": "2025-06-06T06:56:33.000000Z"
      }
    ],
    "meta": {
      "total": 1,
      "per_page": 15,
      "current_page": 1,
      "last_page": 1,
      "from": 1,
      "to": 1
    }
  },
  "timestamp": "2025-06-06T06:56:33.228620Z"
}
```

#### 5.2 创建组织
- **端点**: `POST /api/v1/organisations`
- **描述**: 创建新组织
- **认证**: 需要Bearer Token
- **请求参数**:
  - `name` (必填): 组织名称
  - `code` (必填): 组织代码，必须唯一
  - `details` (可选): 详细信息，JSON格式
  - `remarks` (可选): 备注
  - `status` (可选): 状态 (pending, active, suspended)，默认为pending
- **响应示例**:
```json
{
  "success": true,
  "message": "组织创建成功",
  "data": {
    "id": 1,
    "name": "Test Organisation",
    "code": "TEST001",
    "details": {
      "industry": "Technology"
    },
    "remarks": "Test remarks",
    "status": "pending",
    "is_active": false,
    "is_pending": true,
    "is_suspended": false,
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T06:56:33.000000Z"
  },
  "timestamp": "2025-06-06T06:56:33.228620Z"
}
```

#### 5.3 获取组织详情
- **端点**: `GET /api/v1/organisations/{id}`
- **描述**: 获取指定组织的详细信息
- **认证**: 需要Bearer Token
- **响应示例**:
```json
{
  "success": true,
  "message": "获取组织详情成功",
  "data": {
    "id": 1,
    "name": "Tech Solutions Ltd",
    "code": "TECH001",
    "details": {
      "industry": "Technology",
      "size": "Medium",
      "founded": 2020
    },
    "remarks": "Leading technology solutions provider",
    "status": "active",
    "is_active": true,
    "is_pending": false,
    "is_suspended": false,
    "users_count": 5,
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T06:56:33.000000Z"
  },
  "timestamp": "2025-06-06T06:56:33.228620Z"
}
```

#### 5.4 更新组织
- **端点**: `PUT /api/v1/organisations/{id}`
- **描述**: 更新组织信息
- **认证**: 需要Bearer Token
- **请求参数**: 所有参数都是可选的
  - `name`: 组织名称
  - `code`: 组织代码，必须唯一
  - `details`: 详细信息，JSON格式
  - `remarks`: 备注
  - `status`: 状态 (pending, active, suspended)
- **响应示例**:
```json
{
  "success": true,
  "message": "组织更新成功",
  "data": {
    "id": 1,
    "name": "Updated Organisation Name",
    "code": "UPD001",
    "details": {
      "industry": "Technology",
      "size": "Large"
    },
    "remarks": "Updated remarks",
    "status": "active",
    "is_active": true,
    "is_pending": false,
    "is_suspended": false,
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 5.5 暂停组织
- **端点**: `POST /api/v1/organisations/{id}/suspend`
- **描述**: 暂停组织（将状态设置为suspended）
- **认证**: 需要Bearer Token
- **响应示例**:
```json
{
  "success": true,
  "message": "组织暂停成功",
  "data": {
    "id": 1,
    "name": "Tech Solutions Ltd",
    "code": "TECH001",
    "status": "suspended",
    "is_active": false,
    "is_pending": false,
    "is_suspended": true,
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 组织状态说明
- **pending**: 由系统管理员新建，给所有者邮箱发送了邀请邮件，但所有者尚未完成注册
- **active**: 所有者已经通过邮件中的邀请链接成功入驻，成为了该组织的所有者
- **suspended**: 该组织已被系统管理员停用，该组织所属的普通用户无法执行任何操作

### 6. 角色管理

#### 6.1 获取角色列表
- **端点**: `GET /api/v1/roles`
- **描述**: 获取当前用户组织的所有角色
- **认证**: 需要Bearer Token
- **权限**: 仅限Root和管理员角色访问
- **响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "root",
      "guard_name": "api",
      "organisation_id": 1,
      "created_at": "2025-06-06T06:56:33.000000Z",
      "updated_at": "2025-06-06T06:56:33.000000Z"
    },
    {
      "id": 2,
      "name": "admin",
      "guard_name": "api",
      "organisation_id": 1,
      "created_at": "2025-06-06T06:56:33.000000Z",
      "updated_at": "2025-06-06T06:56:33.000000Z"
    }
  ],
  "message": "Roles retrieved successfully",
  "timestamp": "2025-06-06T06:56:33.228620Z"
}
```

#### 6.2 创建角色
- **端点**: `POST /api/v1/roles`
- **描述**: 创建新角色
- **认证**: 需要Bearer Token
- **权限**: 仅限Root和管理员角色访问
- **请求参数**:
  - `name` (必填): 角色名称
  - `guard_name` (可选): 守卫名称，默认为'api'
- **响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 5,
    "name": "custom-role",
    "guard_name": "api",
    "organisation_id": 1,
    "created_at": "2025-06-06T07:00:00.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "message": "Role created successfully",
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 6.3 获取角色详情
- **端点**: `GET /api/v1/roles/{id}`
- **描述**: 获取指定角色的详细信息
- **认证**: 需要Bearer Token
- **权限**: 仅限Root和管理员角色访问
- **响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "root",
    "guard_name": "api",
    "organisation_id": 1,
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T06:56:33.000000Z"
  },
  "message": "Role retrieved successfully",
  "timestamp": "2025-06-06T06:56:33.228620Z"
}
```

#### 6.4 更新角色
- **端点**: `PUT /api/v1/roles/{id}`
- **描述**: 更新角色信息
- **认证**: 需要Bearer Token
- **权限**: 仅限Root和管理员角色访问
- **请求参数**:
  - `name` (可选): 角色名称
  - `guard_name` (可选): 守卫名称
- **响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "updated-role",
    "guard_name": "api",
    "organisation_id": 1,
    "created_at": "2025-06-06T06:56:33.000000Z",
    "updated_at": "2025-06-06T07:00:00.000000Z"
  },
  "message": "Role updated successfully",
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 6.5 删除角色
- **端点**: `DELETE /api/v1/roles/{id}`
- **描述**: 删除指定角色
- **认证**: 需要Bearer Token
- **权限**: 仅限Root和管理员角色访问
- **响应示例**:
```json
{
  "success": true,
  "message": "Role deleted successfully",
  "timestamp": "2025-06-06T07:00:00.228620Z"
}
```

#### 角色说明
系统预定义了以下四种角色：
- **Root**: 系统初始化时设置的第一个管理员账户，拥有最高权限
- **管理员 (Admin)**: 系统管理员，拥有管理权限
- **组织所有者 (Owner)**: 组织的所有者，拥有组织内的管理权限
- **成员 (Member)**: 普通用户，拥有基本权限

#### 权限模型说明

系统采用基于角色的访问控制（RBAC），不同角色对用户管理功能有不同的权限：

##### 系统管理员权限（Root/Admin）
- 可以访问和管理所有用户
- 可以在任何组织中创建、更新、暂停、激活用户
- 可以管理用户与组织的关联关系
- 不受组织边界限制

##### 组织所有者权限（Owner）
- 只能访问和管理自己组织内的用户
- 可以在自己的组织中创建新用户
- 可以更新、暂停、激活同组织的用户
- **可以管理用户与自己拥有的组织的关联关系（添加/移除用户）**
- 无法访问其他组织的用户

##### 组织成员权限（Member）
- 只能查看同组织的用户列表和详情
- 无法进行用户管理操作（创建、更新、暂停等）
- **无法管理用户与组织的关联关系（不能添加/移除用户）**

##### 权限验证机制
- 系统会自动验证当前用户的角色和权限
- 组织用户只能操作与自己有共同组织的用户
- 跨组织访问会被自动拒绝并返回403错误
- 权限不足时会返回422验证错误

### 7. 邀请管理

邀请功能允许组织管理员创建邀请链接，让用户通过链接加入组织并获得相应角色。

#### 7.1 获取邀请列表
- **端点**: `GET /api/v1/invitations`
- **描述**: 获取邀请列表，支持分页和筛选
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以查看所有邀请
  - **组织所有者（Owner）**: 只能查看自己创建的邀请
- **查询参数**:
  - `per_page` (可选): 每页数量，默认15，最大100
  - `model_type` (可选): 关联模型类型筛选
  - `model_id` (可选): 关联模型ID筛选
  - `role` (可选): 角色筛选
  - `status` (可选): 状态筛选
- **响应示例**:
```json
{
  "success": true,
  "message": "Invitations retrieved successfully",
  "data": {
    "data": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "model_type": "App\\Models\\Organisation",
        "model_id": 1,
        "role": "member",
        "created_by_user_id": 1,
        "expires_at": "2025-06-25T12:00:00.000000Z",
        "max_uses": 5,
        "uses": 2,
        "email_restriction": null,
        "created_at": "2025-06-18T12:00:00.000000Z",
        "updated_at": "2025-06-18T12:00:00.000000Z",
        "model": {
          "id": 1,
          "name": "Tech Solutions Ltd",
          "code": "TECH001",
          "status": "active"
        },
        "created_by": {
          "id": 1,
          "name": "Admin User",
          "email": "<EMAIL>"
        }
      }
    ],
    "meta": {
      "total": 1,
      "per_page": 15,
      "current_page": 1,
      "last_page": 1,
      "from": 1,
      "to": 1
    }
  },
  "timestamp": "2025-06-18T12:00:00.000000Z"
}
```

#### 7.2 创建邀请
- **端点**: `POST /api/v1/invitations`
- **描述**: 创建新的邀请链接
- **认证**: 需要Bearer Token
- **权限要求**:
  - **系统管理员（Root/Admin）**: 可以为任何组织创建邀请
  - **组织所有者（Owner）**: 只能为自己拥有的组织创建邀请
- **请求参数**:
  - `model_type` (必填): 关联模型类型，目前支持 "App\Models\Organisation"
  - `model_id` (必填): 关联的组织ID
  - `role` (必填): 邀请角色，支持 "owner", "member"
  - `expires_at` (可选): 过期时间，必须是未来时间
  - `max_uses` (可选): 最大使用次数，1-100之间，默认为1
  - `email_restriction` (可选): 邮箱限制，只有指定邮箱才能使用邀请
- **请求示例**:
```json
{
  "model_type": "App\\Models\\Organisation",
  "model_id": 1,
  "role": "member",
  "expires_at": "2025-06-25T12:00:00Z",
  "max_uses": 10,
  "email_restriction": "<EMAIL>"
}
```
- **响应示例**:
```json
{
  "success": true,
  "message": "Invitation created successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "model_type": "App\\Models\\Organisation",
    "model_id": 1,
    "role": "member",
    "created_by_user_id": 1,
    "expires_at": "2025-06-25T12:00:00.000000Z",
    "max_uses": 10,
    "uses": 0,
    "email_restriction": "<EMAIL>",
    "created_at": "2025-06-18T12:00:00.000000Z",
    "updated_at": "2025-06-18T12:00:00.000000Z",
    "model": {
      "id": 1,
      "name": "Tech Solutions Ltd",
      "code": "TECH001",
      "status": "active"
    },
    "created_by": {
      "id": 1,
      "name": "Admin User",
      "email": "<EMAIL>"
    }
  },
  "timestamp": "2025-06-18T12:00:00.000000Z"
}
```

#### 7.3 查看邀请详情
- **端点**: `GET /api/v1/invitations/{id}`
- **描述**: 查看邀请详细信息，无需认证即可访问
- **认证**: 无需认证（公开访问）
- **响应示例**:
```json
{
  "success": true,
  "message": "Invitation information retrieved successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "model_type": "App\\Models\\Organisation",
    "model_id": 1,
    "role": "member",
    "created_by_user_id": 1,
    "expires_at": "2025-06-25T12:00:00.000000Z",
    "max_uses": 10,
    "uses": 2,
    "email_restriction": null,
    "created_at": "2025-06-18T12:00:00.000000Z",
    "updated_at": "2025-06-18T12:00:00.000000Z",
    "model": {
      "id": 1,
      "name": "Tech Solutions Ltd",
      "code": "TECH001",
      "status": "active"
    },
    "created_by": {
      "id": 1,
      "name": "Admin User",
      "email": "<EMAIL>"
    }
  },
  "timestamp": "2025-06-18T12:00:00.000000Z"
}
```
- **错误响应**:
  - **410 Gone**: 邀请已过期或达到使用上限
  ```json
  {
    "success": false,
    "message": "Invitation link has expired",
    "timestamp": "2025-06-18T12:00:00.000000Z"
  }
  ```

#### 7.4 接受邀请
- **端点**: `POST /api/v1/invitations/{id}/accept`
- **描述**: 接受邀请并加入组织
- **认证**: 需要Bearer Token
- **权限要求**: 已认证用户
- **响应示例**:
```json
{
  "success": true,
  "message": "Invitation accepted successfully, joined organization",
  "data": {
    "user": {
      "id": 2,
      "name": "New User",
      "email": "<EMAIL>",
      "email_verified_at": "2025-06-18T12:00:00.000000Z"
    },
    "organisation": {
      "id": 1,
      "name": "Tech Solutions Ltd",
      "code": "TECH001",
      "status": "active"
    },
    "role": "member"
  },
  "timestamp": "2025-06-18T12:00:00.000000Z"
}
```
- **错误响应**:
  - **401 Unauthorized**: 用户未登录
  - **403 Forbidden**: 权限不足或邮箱不符合限制
  - **410 Gone**: 邀请已过期或达到使用上限
  - **422 Validation Error**: 邀请验证失败

#### 邀请使用流程

1. **管理员创建邀请**
   - 组织管理员或系统管理员调用创建邀请API
   - 系统生成UUID作为邀请ID
   - 返回邀请信息，包含邀请链接

2. **用户访问邀请链接**
   - 用户访问邀请页面（例如：`/join?id=xxx`）
   - 前端调用 `/api/v1/invitations/{id}` 获取邀请信息
   - 系统返回邀请详情（无需登录即可查看基本信息）

3. **用户接受邀请**
   - 用户点击接受邀请按钮（需要先登录）
   - 前端调用 `/api/v1/invitations/{id}/accept`
   - 系统验证邀请有效性
   - 将用户添加到组织并分配角色
   - 设置用户邮箱为已验证状态
   - 增加邀请使用次数
   - 返回成功信息和用户角色信息

#### 安全特性

1. **系统角色保护**: 不允许通过邀请链接分配系统角色（root, admin）
2. **邮箱限制**: 可以限制特定邮箱才能使用邀请
3. **过期时间**: 邀请有过期时间限制
4. **使用次数限制**: 可以限制邀请的最大使用次数
5. **权限验证**: 只有有权限的用户才能创建邀请

## 认证说明

### Bearer Token 认证
所有需要认证的端点都使用Bearer Token认证方式：

```bash
Authorization: Bearer YOUR_TOKEN_HERE
```

### 获取Token流程
1. 调用 `POST /api/v1/auth/login` 端点
2. 从响应中获取 `data.token` 字段
3. 在后续请求的Header中添加 `Authorization: Bearer {token}`

### Token管理
- Token默认永不过期（可在配置中修改）
- 可通过 `/api/v1/auth/logout` 撤销当前Token
- 可通过 `/api/v1/auth/revoke-all-tokens` 撤销所有Token

## 错误代码

| HTTP状态码 | 说明 |
|-----------|------|
| 200 | 请求成功 |
| 401 | 未认证或Token无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 验证失败 |
| 500 | 服务器内部错误 |

## 使用示例

### 完整认证流程示例

```bash
# 1. 登录获取Token
curl -X POST http://localhost/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# 2. 使用Token访问受保护资源
curl -X GET http://localhost/api/v1/user \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Accept: application/json"

# 3. 退出登录
curl -X POST http://localhost/api/v1/auth/logout \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Accept: application/json"
```
