<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Http\JsonResponse;
use Throwable;

/**
 * API Exception Service
 * 
 * Provides standardized error response formatting for API exceptions
 */
final class ApiExceptionService
{
    /**
     * Standard error messages for different exception types
     */
    private const ERROR_MESSAGES = [
        'validation' => 'Validation failed',
        'authentication' => 'Authentication required',
        'authorization' => 'Access denied',
        'permission' => 'Insufficient permissions',
        'not_found' => 'Resource not found',
        'method_not_allowed' => 'Method not allowed',
        'throttle' => 'Too many requests',
        'bad_request' => 'Bad request',
        'server_error' => 'Internal server error',
        'service_unavailable' => 'Service temporarily unavailable',
        'generic' => 'An error occurred while processing your request',
    ];

    /**
     * Create a standardized error response
     */
    public static function createErrorResponse(
        string $message,
        int $statusCode = 500,
        mixed $errors = null,
        ?string $errorCode = null
    ): JsonResponse {
        $response = [
            'success' => false,
            'message' => $message,
            'errors' => $errors,
            'timestamp' => now()->toISOString(),
        ];

        // Add error code if provided
        if ($errorCode) {
            $response['error_code'] = $errorCode;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Get standard error message by type
     */
    public static function getErrorMessage(string $type): string
    {
        return self::ERROR_MESSAGES[$type] ?? self::ERROR_MESSAGES['generic'];
    }

    /**
     * Create validation error response
     */
    public static function validationError(array $errors, ?string $message = null): JsonResponse
    {
        return self::createErrorResponse(
            $message ?? self::getErrorMessage('validation'),
            422,
            $errors,
            'VALIDATION_ERROR'
        );
    }

    /**
     * Create authentication error response
     */
    public static function authenticationError(?string $message = null): JsonResponse
    {
        return self::createErrorResponse(
            $message ?? self::getErrorMessage('authentication'),
            401,
            null,
            'AUTHENTICATION_ERROR'
        );
    }

    /**
     * Create authorization error response
     */
    public static function authorizationError(?string $message = null): JsonResponse
    {
        return self::createErrorResponse(
            $message ?? self::getErrorMessage('authorization'),
            403,
            null,
            'AUTHORIZATION_ERROR'
        );
    }

    /**
     * Create permission error response
     */
    public static function permissionError(?string $message = null): JsonResponse
    {
        return self::createErrorResponse(
            $message ?? self::getErrorMessage('permission'),
            403,
            null,
            'PERMISSION_ERROR'
        );
    }

    /**
     * Create not found error response
     */
    public static function notFoundError(?string $message = null): JsonResponse
    {
        return self::createErrorResponse(
            $message ?? self::getErrorMessage('not_found'),
            404,
            null,
            'NOT_FOUND_ERROR'
        );
    }

    /**
     * Create method not allowed error response
     */
    public static function methodNotAllowedError(?string $message = null): JsonResponse
    {
        return self::createErrorResponse(
            $message ?? self::getErrorMessage('method_not_allowed'),
            405,
            null,
            'METHOD_NOT_ALLOWED_ERROR'
        );
    }

    /**
     * Create throttle error response
     */
    public static function throttleError(?string $message = null): JsonResponse
    {
        return self::createErrorResponse(
            $message ?? self::getErrorMessage('throttle'),
            429,
            null,
            'THROTTLE_ERROR'
        );
    }

    /**
     * Create server error response
     */
    public static function serverError(?string $message = null): JsonResponse
    {
        return self::createErrorResponse(
            $message ?? self::getErrorMessage('server_error'),
            500,
            null,
            'SERVER_ERROR'
        );
    }

    /**
     * Create generic error response for unknown exceptions
     */
    public static function genericError(?string $message = null): JsonResponse
    {
        return self::createErrorResponse(
            $message ?? self::getErrorMessage('generic'),
            500,
            null,
            'GENERIC_ERROR'
        );
    }

    /**
     * Handle HTTP status code based errors
     */
    public static function httpError(int $statusCode, ?string $message = null): JsonResponse
    {
        $defaultMessage = match($statusCode) {
            400 => self::getErrorMessage('bad_request'),
            401 => self::getErrorMessage('authentication'),
            403 => self::getErrorMessage('authorization'),
            404 => self::getErrorMessage('not_found'),
            405 => self::getErrorMessage('method_not_allowed'),
            422 => self::getErrorMessage('validation'),
            429 => self::getErrorMessage('throttle'),
            500 => self::getErrorMessage('server_error'),
            502 => 'Bad gateway',
            503 => self::getErrorMessage('service_unavailable'),
            default => self::getErrorMessage('generic')
        };

        $errorCode = match($statusCode) {
            400 => 'BAD_REQUEST_ERROR',
            401 => 'AUTHENTICATION_ERROR',
            403 => 'AUTHORIZATION_ERROR',
            404 => 'NOT_FOUND_ERROR',
            405 => 'METHOD_NOT_ALLOWED_ERROR',
            422 => 'VALIDATION_ERROR',
            429 => 'THROTTLE_ERROR',
            500 => 'SERVER_ERROR',
            502 => 'BAD_GATEWAY_ERROR',
            503 => 'SERVICE_UNAVAILABLE_ERROR',
            default => 'HTTP_ERROR'
        };

        return self::createErrorResponse(
            $message ?? $defaultMessage,
            $statusCode,
            null,
            $errorCode
        );
    }

    /**
     * Log exception details for debugging (only in debug mode)
     */
    public static function logException(Throwable $exception): void
    {
        if (config('app.debug')) {
            logger()->error('API Exception occurred', [
                'exception' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
            ]);
        }
    }
}
