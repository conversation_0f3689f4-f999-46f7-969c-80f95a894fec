<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use App\Services\EmailVerificationService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

final class GuestUserRegistrationRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Allow unauthenticated access
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email|max:255',
            'password' => 'required|string|min:8|max:255',
            'verification_code' => 'required|string|size:6',
            // organisation_ids is not allowed for guest registration
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Verify verification code
            $email = $this->input('email');
            $verificationCode = $this->input('verification_code');

            if ($email && $verificationCode) {
                $emailVerificationService = app(EmailVerificationService::class);
                if (!$emailVerificationService->verifyCode($email, $verificationCode)) {
                    $validator->errors()->add('verification_code', 'Invalid or expired verification code.');
                }
            }

            // Ensure organisation_ids is not provided for guest registration
            if ($this->has('organisation_ids')) {
                $validator->errors()->add('organisation_ids', 'Organisation assignment is not allowed for guest registration.');
            }
        });
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Name is required',
            'name.string' => 'Name must be a string',
            'name.max' => 'Name cannot exceed 255 characters',
            'email.required' => 'Email address is required',
            'email.email' => 'Email address format is invalid',
            'email.unique' => 'This email address is already in use',
            'email.max' => 'Email address cannot exceed 255 characters',
            'password.required' => 'Password is required',
            'password.string' => 'Password must be a string',
            'password.min' => 'Password must be at least 8 characters',
            'password.max' => 'Password cannot exceed 255 characters',
            'verification_code.required' => 'Verification code is required',
            'verification_code.string' => 'Verification code must be a string',
            'verification_code.size' => 'Verification code must be exactly 6 characters',
        ];
    }
}
