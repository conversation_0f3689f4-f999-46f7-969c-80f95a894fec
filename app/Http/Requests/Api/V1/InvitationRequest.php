<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

final class InvitationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Authorization is handled in the controller
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'model_type' => 'required|string|in:App\Models\Organisation',
            'model_id' => 'required|integer|exists:organisations,id',
            'role' => 'required|string|in:owner,member',
            'expires_at' => 'nullable|date|after:now',
            'max_uses' => 'nullable|integer|min:1|max:100',
            'email_restriction' => 'nullable|email',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'model_type.required' => 'Model type is required',
            'model_type.in' => 'Model type must be a valid organization',
            'model_id.required' => 'Organization ID is required',
            'model_id.exists' => 'Organization does not exist',
            'role.required' => 'Role is required',
            'role.in' => 'Role must be either owner or member',
            'expires_at.date' => 'Expiration date must be a valid date',
            'expires_at.after' => 'Expiration date must be in the future',
            'max_uses.integer' => 'Maximum uses must be a number',
            'max_uses.min' => 'Maximum uses must be at least 1',
            'max_uses.max' => 'Maximum uses cannot exceed 100',
            'email_restriction.email' => 'Email restriction must be a valid email address',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'model_type' => 'Model Type',
            'model_id' => 'Organization ID',
            'role' => 'Role',
            'expires_at' => 'Expiration Date',
            'max_uses' => 'Maximum Uses',
            'email_restriction' => 'Email Restriction',
        ];
    }
}
