<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Constants\ErrorCodes;
use App\Http\Requests\Api\V1\CreateOrganisationRequest;
use App\Http\Requests\Api\V1\UpdateOrganisationRequest;
use App\Http\Resources\Api\V1\OrganisationCollection;
use App\Http\Resources\Api\V1\OrganisationResource;
use App\Models\Organisation;
use App\Services\OrganisationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

final class OrganisationController extends ApiController
{
    public function __construct(
        private readonly OrganisationService $organisationService
    ) {
        // Set up automatic authorization for Organisation resource
        $this->authorizeResource(Organisation::class, 'organisation');
    }

    /**
     * Display a listing of organisations.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = (int) $request->get('per_page', 15);
        $status = $request->get('status');

        $organisations = $status
            ? $this->organisationService->getByStatus($status, $perPage)
            : $this->organisationService->getAll($perPage);

        return $this->successResponse(
            new OrganisationCollection($organisations),
            '获取组织列表成功'
        );
    }

    /**
     * Store a newly created organisation.
     */
    public function store(CreateOrganisationRequest $request): JsonResponse
    {
        $data = $request->validated();

        // Set default status to pending
        $data['status'] = 'pending';

        $organisation = $this->organisationService->create($data);

        return $this->successResponse(
            new OrganisationResource($organisation),
            '组织创建成功',
            201
        );
    }

    /**
     * Display the specified organisation.
     */
    public function show(Organisation $organisation): JsonResponse
    {
        return $this->successResponse(
            new OrganisationResource($organisation->loadCount('users')),
            '获取组织详情成功'
        );
    }

    /**
     * Update the specified organisation.
     */
    public function update(UpdateOrganisationRequest $request, Organisation $organisation): JsonResponse
    {
        $data = $request->validated();
        $this->organisationService->update($organisation, $data);

        return $this->successResponse(
            new OrganisationResource($organisation->fresh()),
            '组织更新成功'
        );
    }



    /**
     * Suspend the specified organisation.
     */
    public function suspend(Organisation $organisation): JsonResponse
    {
        // Check permission for suspend action
        $this->checkPermission('suspend', $organisation);

        if ($organisation->isSuspended()) {
            return $this->errorResponse('组织已经是暂停状态', null, 422, ErrorCodes::ORGANISATION_ALREADY_SUSPENDED);
        }

        $this->organisationService->suspend($organisation);

        return $this->successResponse(
            new OrganisationResource($organisation->fresh()),
            '组织暂停成功'
        );
    }
}
