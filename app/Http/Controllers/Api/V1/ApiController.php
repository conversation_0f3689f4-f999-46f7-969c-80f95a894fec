<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Base API Controller for version 1
 *
 * This controller provides common functionality for all API endpoints
 * including standardized response formats, error handling, and authorization.
 *
 * Uses <PERSON>vel's built-in authorizeResource method from AuthorizesRequests trait.
 * The checkPermission method is provided for custom authorization needs.
 */
abstract class ApiController extends Controller
{

    /**
     * Manually perform authorization check for non-standard CRUD operations.
     * This is useful for custom controller methods that don't follow standard CRUD patterns.
     *
     * @param string $ability The ability/permission to check
     * @param mixed $arguments The arguments to pass to the policy method
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    protected function checkPermission(string $ability, mixed $arguments = []): void
    {
        $this->authorize($ability, $arguments);
    }

    /**
     * Return a standardized success response
     */
    protected function successResponse(
        mixed $data = null,
        string $message = 'Success',
        int $statusCode = 200
    ): JsonResponse {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->toISOString(),
        ], $statusCode);
    }

    /**
     * Return a standardized error response
     */
    protected function errorResponse(
        string $message = 'Error occurred',
        mixed $errors = null,
        int $statusCode = 400,
        ?string $errorCode = null
    ): JsonResponse {
        return \App\Services\ApiExceptionService::createErrorResponse($message, $statusCode, $errors, $errorCode);
    }

    /**
     * Return a standardized validation error response
     */
    protected function validationErrorResponse(
        array $errors,
        string $message = 'Validation failed'
    ): JsonResponse {
        return \App\Services\ApiExceptionService::validationError($errors, $message);
    }

    /**
     * Return a standardized not found response
     */
    protected function notFoundResponse(string $message = 'Resource not found'): JsonResponse
    {
        return \App\Services\ApiExceptionService::notFoundError($message);
    }

    /**
     * Return a standardized unauthorized response
     */
    protected function unauthorizedResponse(string $message = 'Unauthorized'): JsonResponse
    {
        return \App\Services\ApiExceptionService::authenticationError($message);
    }

    /**
     * Return a standardized forbidden response
     */
    protected function forbiddenResponse(string $message = 'Forbidden'): JsonResponse
    {
        return \App\Services\ApiExceptionService::authorizationError($message);
    }
}
