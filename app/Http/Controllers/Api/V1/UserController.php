<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Requests\Api\V1\CreateUserRequest;
use App\Http\Requests\Api\V1\GuestUserRegistrationRequest;
use App\Http\Requests\Api\V1\UpdateUserRequest;
use App\Http\Resources\Api\V1\CurrentUserResource;
use App\Http\Resources\Api\V1\UserCollection;
use App\Http\Resources\Api\V1\UserResource;
use App\Models\User;
use App\Services\EmailVerificationService;
use App\Services\PermissionService;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

final class UserController extends ApiController
{
    public function __construct(
        private readonly UserService $userService,
        private readonly PermissionService $permissionService,
        private readonly EmailVerificationService $emailVerificationService
    ) {
        // Apply authorization to all methods except register and sendVerificationCode
        $this->authorizeResource(User::class, 'user', [
            'except' => ['register']
        ]);
    }

    /**
     * Get current authenticated user information with roles.
     */
    public function getCurrentUser(Request $request): JsonResponse
    {
        $user = $request->user();
        $roleInfo = $this->permissionService->getUserCompleteRoleInfo($user);

        // Load organisations relationship and attach role info
        $user->load('organisations');
        $user->roles = $roleInfo;

        return $this->successResponse(
            new CurrentUserResource($user),
            'User information retrieved successfully'
        );
    }

    /**
     * Display a listing of users.
     */
    public function index(Request $request): JsonResponse
    {
        $perPage = (int) $request->get('per_page', 15);
        $organisationId = $request->get('organisation_id') ? (int) $request->get('organisation_id') : null;
        $status = $request->get('status');

        $users = $this->userService->getUsers($perPage, $organisationId, $status, $request->user());

        return $this->successResponse(
            new UserCollection($users),
            '获取用户列表成功'
        );
    }

    /**
     * Store a newly created user (authenticated admin users only).
     */
    public function store(CreateUserRequest $request): JsonResponse
    {
        $data = $request->validated();
        $user = $this->userService->create($data, $request->user());

        return $this->successResponse(
            new UserResource($user->load('organisations')),
            '用户创建成功',
            201
        );
    }

    /**
     * Register a new user (guest registration).
     */
    public function register(GuestUserRegistrationRequest $request): JsonResponse
    {
        $data = $request->validated();
        $user = $this->userService->create($data, null);

        return $this->successResponse(
            new UserResource($user->load('organisations')),
            'User registered successfully',
            201
        );
    }

    /**
     * Send email verification code for guest user registration.
     */
    public function sendVerificationCode(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required|email|max:255|unique:users,email',
        ], [
            'email.required' => 'Email address is required',
            'email.email' => 'Email address format is invalid',
            'email.max' => 'Email address cannot exceed 255 characters',
            'email.unique' => 'This email address is already in use',
        ]);

        $email = $request->input('email');

        // Check if we can resend the code (respects retry interval)
        if (!$this->emailVerificationService->canResendCode($email)) {
            return $this->errorResponse(
                'Please wait before requesting a new verification code.',
                null,
                422
            );
        }

        try {
            $code = $this->emailVerificationService->sendVerificationCode($email);

            return $this->successResponse([
                'message' => 'Verification code sent successfully',
                'expires_in_seconds' => 900, // 15 minutes - verification code expiry
            ], 'Verification code sent to your email address');
        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                null,
                422
            );
        }
    }

    /**
     * Display the specified user.
     */
    public function show(Request $request, User $user): JsonResponse
    {
        return $this->successResponse(
            new UserResource($user->load('organisations')),
            '获取用户详情成功'
        );
    }

    /**
     * Update the specified user.
     */
    public function update(UpdateUserRequest $request, User $user): JsonResponse
    {
        $data = $request->validated();
        $this->userService->update($user, $data, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            '用户更新成功'
        );
    }

    /**
     * Suspend the specified user.
     */
    public function suspend(Request $request, User $user): JsonResponse
    {
        // Check permission for suspend action
        $this->checkPermission('suspend', $user);

        $this->userService->suspend($user, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            '用户暂停成功'
        );
    }

    /**
     * Add user to organisation.
     */
    public function addToOrganisation(Request $request, int $userId, int $organisationId): JsonResponse
    {
        $user = $this->userService->getById($userId);

        if (!$user) {
            return $this->notFoundResponse('用户不存在');
        }

        // Check permission for adding user to organisation
        $this->authorize('addToOrganisation', [$user, $organisationId]);

        $this->userService->addToOrganisation($user, $organisationId, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            '用户已添加到组织'
        );
    }

    /**
     * Remove user from organisation.
     */
    public function removeFromOrganisation(Request $request, int $userId, int $organisationId): JsonResponse
    {
        $user = $this->userService->getById($userId);

        if (!$user) {
            return $this->notFoundResponse('用户不存在');
        }

        // Check permission for removing user from organisation
        $this->authorize('removeFromOrganisation', [$user, $organisationId]);

        $this->userService->removeFromOrganisation($user, $organisationId, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            '用户已从组织中移除'
        );
    }

    /**
     * Sync user organisations.
     */
    public function syncOrganisations(Request $request, int $userId): JsonResponse
    {
        $user = $this->userService->getById($userId);

        if (!$user) {
            return $this->notFoundResponse('用户不存在');
        }

        $organisationIds = $request->input('organisation_ids', []);

        // Check permission for syncing user organisations
        $this->authorize('syncOrganisations', [$user, $organisationIds]);

        $this->userService->syncOrganisations($user, $organisationIds, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            '用户组织关联已同步'
        );
    }

    /**
     * Activate the specified user.
     */
    public function activate(Request $request, User $user): JsonResponse
    {
        // Check permission for activate action
        $this->checkPermission('activate', $user);

        $this->userService->activate($user, $request->user());

        return $this->successResponse(
            new UserResource($user->fresh()->load('organisations')),
            '用户激活成功'
        );
    }
}
