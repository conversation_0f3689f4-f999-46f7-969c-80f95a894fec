<?php

declare(strict_types=1);

namespace App\Constants;

/**
 * Error Codes Constants
 * 
 * Centralized error codes for API responses to ensure consistency
 * and maintainability across the application.
 */
final class ErrorCodes
{
    // Organisation related errors
    public const ORGANISATION_ALREADY_SUSPENDED = 'ORGANISATION_ALREADY_SUSPENDED';
    
    // Invitation related errors
    public const INVITATION_EXPIRED = 'INVITATION_EXPIRED';
    public const INVITATION_USAGE_LIMIT_REACHED = 'INVITATION_USAGE_LIMIT_REACHED';
    public const INVITATION_PROCESSING_ERROR = 'INVITATION_PROCESSING_ERROR';
    
    // User related errors
    public const VERIFICATION_CODE_RETRY_LIMIT = 'VERIFICATION_CODE_RETRY_LIMIT';
    public const VERIFICATION_CODE_SEND_ERROR = 'VERIFICATION_CODE_SEND_ERROR';
    
    // Generic business logic errors
    public const BUSINESS_LOGIC_ERROR = 'BUSINESS_LOGIC_ERROR';
    public const RESOURCE_STATE_CONFLICT = 'RESOURCE_STATE_CONFLICT';
    
    /**
     * Get all available error codes
     * 
     * @return array<string, string>
     */
    public static function getAllCodes(): array
    {
        $reflection = new \ReflectionClass(self::class);
        return $reflection->getConstants();
    }
    
    /**
     * Check if an error code exists
     */
    public static function exists(string $code): bool
    {
        return in_array($code, self::getAllCodes(), true);
    }
}
