<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Remove web-specific middleware for API-only application
        $middleware->remove([
            \Illuminate\Foundation\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Cookie\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
        ]);

        // API-specific middleware
        $middleware->api(prepend: [
            \App\Http\Middleware\RequestLoggingMiddleware::class,
            \App\Http\Middleware\ApiMiddleware::class,
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
        ]);

        // Register middleware aliases
        $middleware->alias([
            'role' => \App\Http\Middleware\RoleMiddleware::class,
            'admin' => \App\Http\Middleware\AdminAccessMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Handle API exceptions with standardized JSON responses using ApiExceptionService

        // Validation errors
        $exceptions->render(function (\Illuminate\Validation\ValidationException $e) {
            return \App\Services\ApiExceptionService::validationError($e->errors());
        });

        // Authentication errors
        $exceptions->render(function (\Illuminate\Auth\AuthenticationException $e) {
            return \App\Services\ApiExceptionService::authenticationError();
        });

        // Authorization errors
        $exceptions->render(function (\Illuminate\Auth\Access\AuthorizationException $e) {
            return \App\Services\ApiExceptionService::authorizationError();
        });

        // Access denied errors (403)
        $exceptions->render(function (\Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException $e) {
            return \App\Services\ApiExceptionService::authorizationError();
        });

        // Permission errors (Spatie)
        $exceptions->render(function (\Spatie\Permission\Exceptions\UnauthorizedException $e) {
            return \App\Services\ApiExceptionService::permissionError();
        });

        // Not found errors
        $exceptions->render(function (\Symfony\Component\HttpKernel\Exception\NotFoundHttpException $e) {
            return \App\Services\ApiExceptionService::notFoundError();
        });

        // Method not allowed errors
        $exceptions->render(function (\Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException $e) {
            return \App\Services\ApiExceptionService::methodNotAllowedError();
        });

        // Throttle errors
        $exceptions->render(function (\Illuminate\Http\Exceptions\ThrottleRequestsException $e) {
            return \App\Services\ApiExceptionService::throttleError();
        });

        // General HTTP exceptions
        $exceptions->render(function (\Symfony\Component\HttpKernel\Exception\HttpException $e) {
            return \App\Services\ApiExceptionService::httpError($e->getStatusCode());
        });

        // Handle all other exceptions
        $exceptions->render(function (\Throwable $e, $request) {
            // Log the exception for debugging
            \App\Services\ApiExceptionService::logException($e);

            // Only show detailed error information in debug mode
            if (config('app.debug')) {
                return null; // Let Laravel handle it with full details
            }

            // In production, return a generic error response
            return \App\Services\ApiExceptionService::genericError();
        });
    })->create();
