<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

final class UserControllerPermissionTest extends TestCase
{
    use RefreshDatabase;

    private User $rootUser;
    private User $adminUser;
    private User $ownerUser1;
    private User $ownerUser2;
    private User $memberUser1;
    private User $memberUser2;
    private User $unrelatedUser;
    
    private Organisation $organisation1;
    private Organisation $organisation2;
    
    private Role $rootRole;
    private Role $adminRole;
    private Role $ownerRole1;
    private Role $ownerRole2;
    private Role $memberRole1;
    private Role $memberRole2;

    private PermissionService $permissionService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);
        $this->createTestData();
        $this->assignRoles();
    }

    private function createTestData(): void
    {
        // Create organisations
        $this->organisation1 = Organisation::factory()->create(['name' => 'Organisation 1']);
        $this->organisation2 = Organisation::factory()->create(['name' => 'Organisation 2']);

        // Create users
        $this->rootUser = User::factory()->create(['name' => 'Root User']);
        $this->adminUser = User::factory()->create(['name' => 'Admin User']);
        $this->ownerUser1 = User::factory()->create(['name' => 'Owner User 1']);
        $this->ownerUser2 = User::factory()->create(['name' => 'Owner User 2']);
        $this->memberUser1 = User::factory()->create(['name' => 'Member User 1']);
        $this->memberUser2 = User::factory()->create(['name' => 'Member User 2']);
        $this->unrelatedUser = User::factory()->create(['name' => 'Unrelated User']);

        // Associate users with organisations
        $this->ownerUser1->organisations()->attach($this->organisation1->id);
        $this->memberUser1->organisations()->attach($this->organisation1->id);
        
        $this->ownerUser2->organisations()->attach($this->organisation2->id);
        $this->memberUser2->organisations()->attach($this->organisation2->id);

        // Create system roles
        $systemRoles = $this->permissionService->createSystemRoles();
        $this->rootRole = collect($systemRoles)->firstWhere('name', 'root');
        $this->adminRole = collect($systemRoles)->firstWhere('name', 'admin');

        // Create organisation roles
        $orgRoles1 = $this->permissionService->createDefaultRoles($this->organisation1->id);
        $this->ownerRole1 = collect($orgRoles1)->firstWhere('name', 'owner');
        $this->memberRole1 = collect($orgRoles1)->firstWhere('name', 'member');

        $orgRoles2 = $this->permissionService->createDefaultRoles($this->organisation2->id);
        $this->ownerRole2 = collect($orgRoles2)->firstWhere('name', 'owner');
        $this->memberRole2 = collect($orgRoles2)->firstWhere('name', 'member');
    }

    private function assignRoles(): void
    {
        // Assign system roles (no team context, use system guard)
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);

        $this->rootUser->guard_name = 'system';
        $this->rootUser->assignRole($this->rootRole);

        $this->adminUser->guard_name = 'system';
        $this->adminUser->assignRole($this->adminRole);

        // Assign organisation roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation1->id);
        $this->ownerUser1->assignRole($this->ownerRole1);
        $this->memberUser1->assignRole($this->memberRole1);

        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation2->id);
        $this->ownerUser2->assignRole($this->ownerRole2);
        $this->memberUser2->assignRole($this->memberRole2);
    }

    // ========================================
    // User List Access Tests
    // ========================================

    public function test_root_can_access_all_users(): void
    {
        // Reset team context for system admin checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        Sanctum::actingAs($this->rootUser);

        $response = $this->getJson('/api/v1/users');
        $response->assertStatus(200);

        // Should see all users
        $users = $response->json('data.data');
        $this->assertGreaterThanOrEqual(7, count($users)); // All test users
    }

    public function test_admin_can_access_all_users(): void
    {
        // Reset team context for system admin checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson('/api/v1/users');
        $response->assertStatus(200);

        // Should see all users
        $users = $response->json('data.data');
        $this->assertGreaterThanOrEqual(7, count($users)); // All test users
    }

    public function test_owner_can_access_organisation_users(): void
    {
        Sanctum::actingAs($this->ownerUser1);

        $response = $this->getJson('/api/v1/users');
        $response->assertStatus(200);
        
        // Should only see users from organisation 1
        $users = $response->json('data.data');
        $userNames = array_column($users, 'name');
        
        $this->assertContains('Owner User 1', $userNames);
        $this->assertContains('Member User 1', $userNames);
        $this->assertNotContains('Owner User 2', $userNames);
        $this->assertNotContains('Member User 2', $userNames);
        $this->assertNotContains('Unrelated User', $userNames);
    }

    public function test_owner_can_filter_by_own_organisation(): void
    {
        Sanctum::actingAs($this->ownerUser1);

        $response = $this->getJson("/api/v1/users?organisation_id={$this->organisation1->id}");
        $response->assertStatus(200);
        
        $users = $response->json('data.data');
        $userNames = array_column($users, 'name');
        
        $this->assertContains('Owner User 1', $userNames);
        $this->assertContains('Member User 1', $userNames);
    }

    public function test_owner_cannot_filter_by_other_organisation(): void
    {
        Sanctum::actingAs($this->ownerUser1);

        $response = $this->getJson("/api/v1/users?organisation_id={$this->organisation2->id}");
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['authorization']);
    }

    public function test_member_can_access_organisation_users(): void
    {
        Sanctum::actingAs($this->memberUser1);

        $response = $this->getJson('/api/v1/users');
        $response->assertStatus(200);
        
        // Should only see users from organisation 1
        $users = $response->json('data.data');
        $userNames = array_column($users, 'name');
        
        $this->assertContains('Owner User 1', $userNames);
        $this->assertContains('Member User 1', $userNames);
        $this->assertNotContains('Owner User 2', $userNames);
        $this->assertNotContains('Member User 2', $userNames);
    }

    public function test_unrelated_user_cannot_access_users(): void
    {
        Sanctum::actingAs($this->unrelatedUser);

        $response = $this->getJson('/api/v1/users');
        $response->assertStatus(403);
    }

    // ========================================
    // User Detail Access Tests
    // ========================================

    public function test_root_can_view_any_user(): void
    {
        // Reset team context for system admin checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        Sanctum::actingAs($this->rootUser);

        $response = $this->getJson("/api/v1/users/{$this->memberUser1->id}");
        $response->assertStatus(200);
        $response->assertJson(['data' => ['name' => 'Member User 1']]);
    }

    public function test_owner_can_view_organisation_user(): void
    {
        Sanctum::actingAs($this->ownerUser1);

        $response = $this->getJson("/api/v1/users/{$this->memberUser1->id}");
        $response->assertStatus(200);
        $response->assertJson(['data' => ['name' => 'Member User 1']]);
    }

    public function test_owner_cannot_view_other_organisation_user(): void
    {
        Sanctum::actingAs($this->ownerUser1);

        $response = $this->getJson("/api/v1/users/{$this->memberUser2->id}");
        $response->assertStatus(403);
        $response->assertJson([
            'success' => false,
            'message' => 'Access denied',
            'error_code' => 'AUTHORIZATION_ERROR'
        ]);
    }

    public function test_member_can_view_organisation_user(): void
    {
        Sanctum::actingAs($this->memberUser1);

        $response = $this->getJson("/api/v1/users/{$this->ownerUser1->id}");
        $response->assertStatus(200);
        $response->assertJson(['data' => ['name' => 'Owner User 1']]);
    }

    public function test_unrelated_user_cannot_view_any_user(): void
    {
        Sanctum::actingAs($this->unrelatedUser);

        $response = $this->getJson("/api/v1/users/{$this->memberUser1->id}");
        $response->assertStatus(403);
        $response->assertJson([
            'success' => false,
            'message' => 'Access denied',
            'error_code' => 'AUTHORIZATION_ERROR'
        ]);
    }

    // ========================================
    // User Creation Tests
    // ========================================

    public function test_root_can_create_user_in_any_organisation(): void
    {
        // Reset team context for system admin checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        Sanctum::actingAs($this->rootUser);

        $userData = [
            'name' => 'New User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'organisation_ids' => [$this->organisation1->id, $this->organisation2->id]
        ];

        $response = $this->postJson('/api/v1/users', $userData);
        $response->assertStatus(201);
        $response->assertJson(['message' => '用户创建成功']);

        // Verify user was created with correct organisations
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertCount(2, $user->organisations);
    }

    public function test_owner_can_create_user_in_own_organisation(): void
    {
        Sanctum::actingAs($this->ownerUser1);

        $userData = [
            'name' => 'New Org User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'organisation_ids' => [$this->organisation1->id]
        ];

        $response = $this->postJson('/api/v1/users', $userData);
        $response->assertStatus(201);
        $response->assertJson(['message' => '用户创建成功']);

        // Verify user was created in correct organisation
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertTrue($user->belongsToOrganisation($this->organisation1->id));
    }

    public function test_owner_cannot_create_user_in_other_organisation(): void
    {
        Sanctum::actingAs($this->ownerUser1);

        $userData = [
            'name' => 'Invalid User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'organisation_ids' => [$this->organisation2->id]
        ];

        $response = $this->postJson('/api/v1/users', $userData);
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['authorization']);
    }

    public function test_owner_creates_user_without_organisation_defaults_to_own(): void
    {
        Sanctum::actingAs($this->ownerUser1);

        $userData = [
            'name' => 'Default Org User',
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];

        $response = $this->postJson('/api/v1/users', $userData);
        $response->assertStatus(201);

        // Verify user was created in owner's organisation
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertTrue($user->belongsToOrganisation($this->organisation1->id));
    }

    public function test_unrelated_user_cannot_create_user(): void
    {
        Sanctum::actingAs($this->unrelatedUser);

        $userData = [
            'name' => 'Unauthorized User',
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];

        $response = $this->postJson('/api/v1/users', $userData);
        $response->assertStatus(403);
    }

    // ========================================
    // User Update Tests
    // ========================================

    public function test_root_can_update_any_user(): void
    {
        // Reset team context for system admin checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        Sanctum::actingAs($this->rootUser);

        $updateData = ['name' => 'Updated Name'];

        $response = $this->putJson("/api/v1/users/{$this->memberUser1->id}", $updateData);
        $response->assertStatus(200);
        $response->assertJson(['message' => '用户更新成功']);

        $this->memberUser1->refresh();
        $this->assertEquals('Updated Name', $this->memberUser1->name);
    }

    public function test_owner_can_update_organisation_user(): void
    {
        Sanctum::actingAs($this->ownerUser1);

        $updateData = ['name' => 'Updated Member Name'];

        $response = $this->putJson("/api/v1/users/{$this->memberUser1->id}", $updateData);
        $response->assertStatus(200);
        $response->assertJson(['message' => '用户更新成功']);

        $this->memberUser1->refresh();
        $this->assertEquals('Updated Member Name', $this->memberUser1->name);
    }

    public function test_owner_cannot_update_other_organisation_user(): void
    {
        Sanctum::actingAs($this->ownerUser1);

        $updateData = ['name' => 'Should Not Update'];

        $response = $this->putJson("/api/v1/users/{$this->memberUser2->id}", $updateData);
        $response->assertStatus(403);
    }

    public function test_unrelated_user_cannot_update_any_user(): void
    {
        Sanctum::actingAs($this->unrelatedUser);

        $updateData = ['name' => 'Unauthorized Update'];

        $response = $this->putJson("/api/v1/users/{$this->memberUser1->id}", $updateData);
        $response->assertStatus(403);
    }

    // ========================================
    // User Suspend/Activate Tests
    // ========================================

    public function test_root_can_suspend_any_user(): void
    {
        // Create a token for the target user first
        $token = $this->memberUser1->createToken('test-token')->plainTextToken;
        $this->assertDatabaseHas('personal_access_tokens', [
            'tokenable_id' => $this->memberUser1->id,
            'tokenable_type' => User::class,
        ]);

        // Reset team context for system admin checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        Sanctum::actingAs($this->rootUser);

        $response = $this->postJson("/api/v1/users/{$this->memberUser1->id}/suspend");
        $response->assertStatus(200);
        $response->assertJson(['message' => '用户暂停成功']);

        // Verify tokens were revoked
        $this->assertDatabaseMissing('personal_access_tokens', [
            'tokenable_id' => $this->memberUser1->id,
            'tokenable_type' => User::class,
        ]);
    }

    public function test_owner_can_suspend_organisation_user(): void
    {
        // Create a token for the target user first
        $token = $this->memberUser1->createToken('test-token')->plainTextToken;

        Sanctum::actingAs($this->ownerUser1);

        $response = $this->postJson("/api/v1/users/{$this->memberUser1->id}/suspend");
        $response->assertStatus(200);
        $response->assertJson(['message' => '用户暂停成功']);

        // Verify tokens were revoked
        $this->assertDatabaseMissing('personal_access_tokens', [
            'tokenable_id' => $this->memberUser1->id,
            'tokenable_type' => User::class,
        ]);
    }

    public function test_owner_cannot_suspend_other_organisation_user(): void
    {
        Sanctum::actingAs($this->ownerUser1);

        $response = $this->postJson("/api/v1/users/{$this->memberUser2->id}/suspend");
        $response->assertStatus(403);
    }

    public function test_root_can_activate_any_user(): void
    {
        // Reset team context for system admin checks
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        Sanctum::actingAs($this->rootUser);

        $response = $this->postJson("/api/v1/users/{$this->memberUser1->id}/activate");
        $response->assertStatus(200);
        $response->assertJson(['message' => '用户激活成功']);
    }

    public function test_owner_can_activate_organisation_user(): void
    {
        Sanctum::actingAs($this->ownerUser1);

        $response = $this->postJson("/api/v1/users/{$this->memberUser1->id}/activate");
        $response->assertStatus(200);
        $response->assertJson(['message' => '用户激活成功']);
    }

    public function test_owner_cannot_activate_other_organisation_user(): void
    {
        Sanctum::actingAs($this->ownerUser1);

        $response = $this->postJson("/api/v1/users/{$this->memberUser2->id}/activate");
        $response->assertStatus(403);
    }

    public function test_unrelated_user_cannot_suspend_any_user(): void
    {
        Sanctum::actingAs($this->unrelatedUser);

        $response = $this->postJson("/api/v1/users/{$this->memberUser1->id}/suspend");
        $response->assertStatus(403);
    }

    public function test_unrelated_user_cannot_activate_any_user(): void
    {
        Sanctum::actingAs($this->unrelatedUser);

        $response = $this->postJson("/api/v1/users/{$this->memberUser1->id}/activate");
        $response->assertStatus(403);
    }
}
