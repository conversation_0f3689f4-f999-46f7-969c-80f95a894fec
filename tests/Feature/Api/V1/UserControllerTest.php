<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V1;

use App\Models\Organisation;
use App\Models\User;
use App\Services\EmailVerificationService;
use App\Services\PermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

final class UserControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $adminUser;
    private string $token;
    private Organisation $organisation1;
    private Organisation $organisation2;
    private PermissionService $permissionService;
    private EmailVerificationService $emailVerificationService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->permissionService = app(PermissionService::class);
        $this->emailVerificationService = app(EmailVerificationService::class);

        // Create test organisations
        $this->organisation1 = Organisation::factory()->create([
            'name' => 'Test Organisation 1',
            'code' => 'TEST001',
            'status' => 'active',
        ]);

        $this->organisation2 = Organisation::factory()->create([
            'name' => 'Test Organisation 2',
            'code' => 'TEST002',
            'status' => 'active',
        ]);

        // Create admin user with system admin privileges
        $this->adminUser = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        // Create system admin role and assign to user
        $adminRole = $this->permissionService->createRole('admin', 'system', null);
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->adminUser->guard_name = 'system';
        $this->adminUser->assignRole($adminRole);

        $this->token = $this->adminUser->createToken('test-token')->plainTextToken;
    }

    public function test_can_get_users_list(): void
    {
        // Create test users with organisations
        $user1 = User::factory()->create(['name' => 'User 1']);
        $user1->organisations()->attach([$this->organisation1->id, $this->organisation2->id]);

        $user2 = User::factory()->create(['name' => 'User 2']);
        $user2->organisations()->attach([$this->organisation1->id]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v1/users');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'email',
                            'email_verified_at',
                            'organisations',
                            'created_at',
                            'updated_at',
                        ]
                    ],
                    'meta' => [
                        'total',
                        'per_page',
                        'current_page',
                        'last_page',
                        'from',
                        'to',
                    ]
                ],
                'timestamp'
            ])
            ->assertJson([
                'success' => true,
                'message' => '获取用户列表成功',
            ]);
    }

    public function test_can_filter_users_by_organisation(): void
    {
        // Create users with different organisation associations
        $user1 = User::factory()->create(['name' => 'User 1']);
        $user1->organisations()->attach([$this->organisation1->id]);

        $user2 = User::factory()->create(['name' => 'User 2']);
        $user2->organisations()->attach([$this->organisation2->id]);

        $user3 = User::factory()->create(['name' => 'User 3']);
        $user3->organisations()->attach([$this->organisation1->id, $this->organisation2->id]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson("/api/v1/users?organisation_id={$this->organisation1->id}");

        $response->assertStatus(200);

        $users = $response->json('data.data');
        $this->assertCount(2, $users); // user1 and user3 should be returned
    }

    public function test_authenticated_admin_can_create_user_with_organisations(): void
    {
        $data = [
            'name' => 'New User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'organisation_ids' => [$this->organisation1->id, $this->organisation2->id],
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/users', $data);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'email',
                    'organisations',
                    'created_at',
                    'updated_at',
                ],
                'timestamp'
            ])
            ->assertJson([
                'success' => true,
                'message' => '用户创建成功',
                'data' => [
                    'name' => 'New User',
                    'email' => '<EMAIL>',
                ]
            ]);

        // Verify user was created in database
        $this->assertDatabaseHas('users', [
            'name' => 'New User',
            'email' => '<EMAIL>',
        ]);

        // Verify user-organisation associations
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertCount(2, $user->organisations);
        $this->assertTrue($user->organisations->contains($this->organisation1));
        $this->assertTrue($user->organisations->contains($this->organisation2));
    }

    public function test_authenticated_admin_can_create_user_without_organisations(): void
    {
        $data = [
            'name' => 'Simple User',
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/users', $data);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => '用户创建成功',
                'data' => [
                    'name' => 'Simple User',
                    'email' => '<EMAIL>',
                ]
            ]);

        // Verify user has no organisations
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertCount(0, $user->organisations);
    }

    public function test_guest_can_register_with_valid_verification_code(): void
    {
        $email = '<EMAIL>';
        $code = $this->emailVerificationService->sendVerificationCode($email);

        $data = [
            'name' => 'Guest User',
            'email' => $email,
            'password' => 'password123',
            'verification_code' => $code,
        ];

        $response = $this->postJson('/api/v1/users/register', $data);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'User registered successfully',
                'data' => [
                    'name' => 'Guest User',
                    'email' => $email,
                ]
            ]);

        // Verify user was created in database
        $this->assertDatabaseHas('users', [
            'name' => 'Guest User',
            'email' => $email,
        ]);

        // Verify user has no organisations (guest registration)
        $user = User::where('email', $email)->first();
        $this->assertCount(0, $user->organisations);
    }

    public function test_guest_registration_fails_with_invalid_verification_code(): void
    {
        $data = [
            'name' => 'Guest User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'verification_code' => '123456', // Invalid code
        ];

        $response = $this->postJson('/api/v1/users/register', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['verification_code']);
    }

    public function test_guest_registration_fails_without_verification_code(): void
    {
        $data = [
            'name' => 'Guest User',
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        $response = $this->postJson('/api/v1/users/register', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['verification_code']);
    }

    public function test_guest_registration_fails_with_organisation_ids(): void
    {
        $email = '<EMAIL>';
        $code = $this->emailVerificationService->sendVerificationCode($email);

        $data = [
            'name' => 'Guest User',
            'email' => $email,
            'password' => 'password123',
            'verification_code' => $code,
            'organisation_ids' => [$this->organisation1->id], // Not allowed for guests
        ];

        $response = $this->postJson('/api/v1/users/register', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['organisation_ids']);
    }

    public function test_create_user_validation_fails_for_missing_required_fields(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/users', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'email', 'password']);
    }

    public function test_create_user_validation_fails_for_duplicate_email(): void
    {
        User::factory()->create(['email' => '<EMAIL>']);

        $data = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/users', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_create_user_validation_fails_for_invalid_organisation_ids(): void
    {
        $data = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'organisation_ids' => [999, 1000], // Non-existent organisation IDs
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson('/api/v1/users', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['organisation_ids.0', 'organisation_ids.1']);
    }

    public function test_can_show_user_with_organisations(): void
    {
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
        $user->organisations()->attach([$this->organisation1->id, $this->organisation2->id]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson("/api/v1/users/{$user->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '获取用户详情成功',
                'data' => [
                    'id' => $user->id,
                    'name' => 'Test User',
                    'email' => '<EMAIL>',
                ]
            ]);

        // Verify organisations are included
        $organisations = $response->json('data.organisations');
        $this->assertCount(2, $organisations);
    }

    public function test_show_user_returns_404_for_non_existent(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->getJson('/api/v1/users/999');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Resource not found',
            ]);
    }

    public function test_can_update_user_organisations(): void
    {
        $user = User::factory()->create([
            'name' => 'Old Name',
            'email' => '<EMAIL>',
        ]);
        $user->organisations()->attach([$this->organisation1->id]);

        $updateData = [
            'name' => 'New Name',
            'organisation_ids' => [$this->organisation2->id],
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->putJson("/api/v1/users/{$user->id}", $updateData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '用户更新成功',
                'data' => [
                    'id' => $user->id,
                    'name' => 'New Name',
                ]
            ]);

        // Verify user organisations were updated
        $user->refresh();
        $this->assertCount(1, $user->organisations);
        $this->assertTrue($user->organisations->contains($this->organisation2));
        $this->assertFalse($user->organisations->contains($this->organisation1));
    }

    public function test_update_user_returns_404_for_non_existent(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->putJson('/api/v1/users/999', ['name' => 'New Name']);

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Resource not found',
            ]);
    }

    public function test_can_add_user_to_organisation(): void
    {
        $user = User::factory()->create();

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson("/api/v1/users/{$user->id}/organisations/{$this->organisation1->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '用户已添加到组织',
            ]);

        // Verify user was added to organisation
        $user->refresh();
        $this->assertTrue($user->organisations->contains($this->organisation1));
    }

    public function test_add_user_to_organisation_is_idempotent(): void
    {
        $user = User::factory()->create();
        $user->organisations()->attach($this->organisation1->id);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson("/api/v1/users/{$user->id}/organisations/{$this->organisation1->id}");

        $response->assertStatus(200);

        // Verify user is still only associated once
        $user->refresh();
        $this->assertCount(1, $user->organisations);
    }

    public function test_can_remove_user_from_organisation(): void
    {
        $user = User::factory()->create();
        $user->organisations()->attach([$this->organisation1->id, $this->organisation2->id]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->deleteJson("/api/v1/users/{$user->id}/organisations/{$this->organisation1->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '用户已从组织中移除',
            ]);

        // Verify user was removed from organisation
        $user->refresh();
        $this->assertCount(1, $user->organisations);
        $this->assertFalse($user->organisations->contains($this->organisation1));
        $this->assertTrue($user->organisations->contains($this->organisation2));
    }

    public function test_can_sync_user_organisations(): void
    {
        $user = User::factory()->create();
        $user->organisations()->attach([$this->organisation1->id]);

        $data = [
            'organisation_ids' => [$this->organisation2->id],
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->putJson("/api/v1/users/{$user->id}/organisations", $data);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '用户组织关联已同步',
            ]);

        // Verify user organisations were synced
        $user->refresh();
        $this->assertCount(1, $user->organisations);
        $this->assertFalse($user->organisations->contains($this->organisation1));
        $this->assertTrue($user->organisations->contains($this->organisation2));
    }

    public function test_can_sync_user_organisations_with_empty_array(): void
    {
        $user = User::factory()->create();
        $user->organisations()->attach([$this->organisation1->id, $this->organisation2->id]);

        $data = [
            'organisation_ids' => [],
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->putJson("/api/v1/users/{$user->id}/organisations", $data);

        $response->assertStatus(200);

        // Verify all organisations were removed
        $user->refresh();
        $this->assertCount(0, $user->organisations);
    }

    public function test_can_suspend_user(): void
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $this->token,
        ])->postJson("/api/v1/users/{$user->id}/suspend");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '用户暂停成功',
            ]);

        // Verify user's tokens were revoked
        $this->assertDatabaseMissing('personal_access_tokens', [
            'tokenable_id' => $user->id,
            'tokenable_type' => User::class,
        ]);
    }

    public function test_protected_user_endpoints_require_authentication(): void
    {
        $user = User::factory()->create();

        // Test index endpoint
        $response = $this->getJson('/api/v1/users');
        $response->assertStatus(401);

        // Test store endpoint (now requires authentication)
        $response = $this->postJson('/api/v1/users', []);
        $response->assertStatus(401);

        // Note: register endpoint is public for guest registration

        // Test show endpoint
        $response = $this->getJson("/api/v1/users/{$user->id}");
        $response->assertStatus(401);

        // Test update endpoint
        $response = $this->putJson("/api/v1/users/{$user->id}", []);
        $response->assertStatus(401);

        // Test suspend endpoint
        $response = $this->postJson("/api/v1/users/{$user->id}/suspend");
        $response->assertStatus(401);

        // Test organisation association endpoints
        $response = $this->postJson("/api/v1/users/{$user->id}/organisations/1");
        $response->assertStatus(401);

        $response = $this->deleteJson("/api/v1/users/{$user->id}/organisations/1");
        $response->assertStatus(401);

        $response = $this->putJson("/api/v1/users/{$user->id}/organisations", []);
        $response->assertStatus(401);
    }

    public function test_can_send_verification_code(): void
    {
        $email = '<EMAIL>';

        $response = $this->postJson('/api/v1/users/send-verification-code', [
            'email' => $email,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Verification code sent to your email address',
            ])
            ->assertJsonStructure([
                'data' => [
                    'message',
                    'expires_in_seconds',
                ]
            ]);

        // Verify code was cached in Redis
        $this->assertTrue($this->emailVerificationService->hasValidCode($email));
    }

    public function test_send_verification_code_validation_fails_for_invalid_email(): void
    {
        $response = $this->postJson('/api/v1/users/send-verification-code', [
            'email' => 'invalid-email',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_send_verification_code_fails_for_existing_email(): void
    {
        User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->postJson('/api/v1/users/send-verification-code', [
            'email' => '<EMAIL>',
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_cannot_send_verification_code_if_already_sent(): void
    {
        $email = '<EMAIL>';

        // Send first verification code
        $this->emailVerificationService->sendVerificationCode($email);

        // Try to send another one immediately
        $response = $this->postJson('/api/v1/users/send-verification-code', [
            'email' => $email,
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Please wait before requesting a new verification code.',
                'error_code' => 'VERIFICATION_CODE_RETRY_LIMIT',
            ]);
    }

    protected function tearDown(): void
    {
        // Clean up cache after each test
        Cache::flush();
        parent::tearDown();
    }
}
